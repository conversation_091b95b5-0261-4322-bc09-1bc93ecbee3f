{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://spring-maven-api-lee.drke.ok8s.aa.com", "description": "Generated server url"}], "tags": [{"name": "Actuator", "description": "Monitor and interact", "externalDocs": {"description": "Spring Boot Actuator Web API Documentation", "url": "https://docs.spring.io/spring-boot/docs/current/actuator-api/html/"}}], "paths": {"/greeting": {"get": {"tags": ["greeting-controller"], "summary": "Get a custom greeting", "operationId": "greeting", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string", "default": "World"}}], "responses": {"200": {"description": "Returning a greeting", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Greeting"}}}}}}}, "/actuator": {"get": {"tags": ["Actuator"], "summary": "Actuator root web endpoint", "operationId": "links_0", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Link"}}}}}}}}}, "/actuator/info": {"get": {"tags": ["Actuator"], "summary": "Actuator web endpoint 'info'", "operationId": "handle_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}, "/actuator/health": {"get": {"tags": ["Actuator"], "summary": "Actuator web endpoint 'health'", "operationId": "handle_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}, "/actuator/health/**": {"get": {"tags": ["Actuator"], "summary": "Actuator web endpoint 'health-path'", "operationId": "handle_3", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}}, "components": {"schemas": {"Greeting": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "content": {"type": "string"}}}, "Link": {"type": "object", "properties": {"href": {"type": "string"}, "templated": {"type": "boolean"}}}}}}