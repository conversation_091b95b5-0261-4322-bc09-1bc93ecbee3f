package com.aa.ccrecon.gl.validation;

import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.config.WebClientConfig;
import com.aa.ccrecon.gl.model.request.SapRequest;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.cloud.stream.binder.test.TestChannelBinderConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.InputStream;
import java.io.InputStreamReader;

@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest
@EnableConfigurationProperties(value = SapRequestConfig.class)
@Import({TestChannelBinderConfiguration.class})
@ExtendWith(OutputCaptureExtension.class)
@ActiveProfiles("test")
public class TestValidation {
    private final Logger logger = LoggerFactory.getLogger(TestValidation.class);

    @MockitoBean
    WebClient webClient;

    @MockitoBean
    WebClientConfig webClientConfig;

    @MockitoBean
    DirectSapConnector sapConnector;

    @Autowired
    ValidationService validationService;

    @MockitoBean
    DirectSapConnector directSapConnector;

    @Test
    void testValidation (CapturedOutput output) {
        Gson g = new Gson();

        InputStream is = getClass().getResourceAsStream("/json/requestvalidation2.json");
        InputStreamReader fis = new InputStreamReader(is);
        SapRequest r = g.fromJson(fis, SapRequest.class);

        validationService.printValidationErrors(r);
        assert (output.getOut().contains("Error in validation"));
        logger.info("{}", output);

    }

}
