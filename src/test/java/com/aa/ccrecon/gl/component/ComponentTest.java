package com.aa.ccrecon.gl.component;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.TestComponent;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.AppConfig;
import com.aa.ccrecon.gl.config.TestServiceBusConfig;
import com.aa.ccrecon.gl.config.WebClientConfig;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import com.aa.ccrecon.gl.validation.ValidationService;
import com.aa.itfacs.pmt.mask.Masked;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.itfacs.pmt.controller.StreamInputRequest;
import com.itfacs.pmt.controller.StreamOutputsResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.cloud.stream.binder.test.EnableTestBinder;
import org.springframework.cloud.stream.binder.test.OutputDestination;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.jdbc.Sql;

import java.io.IOException;
import java.util.List;

import static com.aa.ccrecon.gl.model.GlResponseEnum.GLPOSTING_FAILEDCONNECTION;
import static com.aa.ccrecon.gl.utils.AppConstants.APP_NAME;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ExtendWith(OutputCaptureExtension.class)
@EnableTestBinder
@ContextConfiguration(classes = TestServiceBusConfig.class)
@ActiveProfiles("test")
public class ComponentTest extends TestContainerSetup {
    @Autowired
    private TestComponent testComponent;

    @Autowired
    private OutputDestination outputDestination;

    @MockitoBean
    private DirectSapConnector sapConnector;

    @MockitoSpyBean
    AppConfig appConfig;

    @MockitoBean
    ValidationService validationService;

    @MockitoBean
    WebClientConfig webClientConfig;

    @MockitoBean
    DirectSapConnector directSapConnector;

    private static ObjectMapper jsonObjectMapper;
    private static ObjectMapper messageObjectMapper;

    private static final String INCOMING_REC_ACCT_Q = "consumeAggregatedGeneralLedgerTransactionDetailsMessage";
    private static final String INCOMING_SETTLEMENT_ACCT_Q = "consumeAggregatedSettlementMessage";
    private static final String OUTGOING_SUCCESS_Q = "supplyAggregatedGeneralLedgerTransactionDetailsMessage";
    private static final String EXCEPTION_Q = "sendExceptionMessage";

    @BeforeAll
    public static void onlyOnce() {
        jsonObjectMapper = new ObjectMapper();
        jsonObjectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        jsonObjectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        messageObjectMapper = new ObjectMapper();
        messageObjectMapper.registerModule(new JavaTimeModule());
        messageObjectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @BeforeEach
    public void init() {
        outputDestination.clear();

        Logger rootLogger = (Logger) LoggerFactory.getLogger("com.aa.ccrecon.gl");
        rootLogger.setLevel(Level.DEBUG);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenAggregatedGlSuccess_ThenVerifyNoRetries(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyNoRetries(output, aggregatedGeneralLedgerTransactionDetails, INCOMING_REC_ACCT_Q);

        assertThat(output.getAll(), containsString("\"salesSource\": \"COMPANY\""));
        assertThat(output.getAll(), containsString("\"transactionType\": \"SALE\""));
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenUatpAggregatedGlSuccess_ThenVerifyNoRetries(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "UATP-3", 1, MessageType.AGGREGATED_GL_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyNoRetries(output, aggregatedGeneralLedgerTransactionDetails, INCOMING_REC_ACCT_Q);

        assertThat(output.getAll(), containsString("\"salesSource\": \"COMPANY\""));
        assertThat(output.getAll(), containsString("\"transactionType\": \"SALE\""));
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenAggregatedGlFailFirstTime_ThenVerifyOneRetry(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyOneRetry(output, aggregatedGeneralLedgerTransactionDetails);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenUatpAggregatedGlFailFirstTime_ThenVerifyOneRetry(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "UATP-3", 1, MessageType.AGGREGATED_GL_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyOneRetry(output, aggregatedGeneralLedgerTransactionDetails);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenAggregatedGlSapResponseException_ThenVerifyMaxRetries(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyMaxRetries(output, aggregatedGeneralLedgerTransactionDetails);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenAggregatedGeneralLedgerTransactionDetails_WhenUatpAggregatedGlSapResponseException_ThenVerifyMaxRetries(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "UATP-3", 1, MessageType.AGGREGATED_GL_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyMaxRetries(output, aggregatedGeneralLedgerTransactionDetails);
    }

    @DisplayName("Given a settlement message, when the message is processed, then verify no retries")
    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void settlementMessageSuccess(CapturedOutput output) throws SapResponseException, IOException {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.SETTLEMENT_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        verifyNoRetries(output, aggregatedGeneralLedgerTransactionDetails, INCOMING_SETTLEMENT_ACCT_Q);
    }

    private void verifyNoRetries(CapturedOutput output, AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails, String incomingQ) throws SapResponseException, IOException {
        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
        GlPostingResponse sapResponse = new GlPostingResponse();

        doReturn(sapResponse)
                .when(sapConnector).postToSap(any(GeneralLedgerEnvelope.class));

        final StreamOutputsResponse outputsResponse = testComponent.processEvent(new StreamInputRequest(1, 1, incomingQ, List.of(EXCEPTION_Q, OUTGOING_SUCCESS_Q), aggregatedTransaction));

        Assertions.assertFalse(output.getAll().contains("Going to add message " + Masked.objectToMaskedString(aggregatedGeneralLedgerTransactionDetails) + " to Sinks.Many."));
        Assertions.assertFalse(output.getAll().contains("Exhausted '" + appConfig.getMessageRetryCountLimit() + "' retries for Global ID: " + aggregatedGeneralLedgerTransactionDetails.getGlobalId()));

        byte[] msg = (byte[]) outputsResponse.getOutputs().get(OUTGOING_SUCCESS_Q + "0");
        Assertions.assertNull(msg);
    }

    private void verifyOneRetry(CapturedOutput output, AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails) throws SapResponseException, IOException {
        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        doThrow(new SapResponseException("body", "message", 500))
                .doReturn(new GlPostingResponse())
                .when(sapConnector).postToSap(any(GeneralLedgerEnvelope.class));

        final StreamOutputsResponse outputsResponse = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_REC_ACCT_Q, List.of(EXCEPTION_Q, OUTGOING_SUCCESS_Q), aggregatedTransaction));

        Assertions.assertTrue(output.getAll().contains("Going to add message " + Masked.objectToMaskedString(aggregatedTransaction) + " to Sinks.Many."));

        byte[] msg = (byte[]) outputsResponse.getOutputs().get(OUTGOING_SUCCESS_Q + "0");
        String globalId = TestModelExtractor.getGlobalId.apply(jsonObjectMapper, msg).orElse(null);

        final StreamOutputsResponse outputsResponse2 = testComponent.processEvent(new StreamInputRequest(1, 2, INCOMING_REC_ACCT_Q, List.of(EXCEPTION_Q, OUTGOING_SUCCESS_Q), aggregatedTransaction));

        Assertions.assertFalse(output.getAll().contains("Exhausted '" + appConfig.getMessageRetryCountLimit() + "' retries for Global ID: " + globalId));

        byte[] msg2 = (byte[]) outputsResponse2.getOutputs().get(OUTGOING_SUCCESS_Q + "0");
        Assertions.assertNull(msg2);
    }

    private void verifyMaxRetries(CapturedOutput output, AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails) throws SapResponseException, IOException {
        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        doThrow(new SapResponseException("body", "message", 500))
                .when(sapConnector).postToSap(any(GeneralLedgerEnvelope.class));

        StreamOutputsResponse outputsResponse = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_REC_ACCT_Q, List.of(EXCEPTION_Q, OUTGOING_SUCCESS_Q), aggregatedTransaction));

        int scheduleMessageRetryCount = 0;
        do {
            Assertions.assertTrue(output.getAll().contains("Going to add message " + Masked.objectToMaskedString(aggregatedTransaction) + " to Sinks.Many."));

            byte[] msg = (byte[]) outputsResponse.getOutputs().get(OUTGOING_SUCCESS_Q + "0");
            aggregatedTransaction = TestModelExtractor.getAggregatedTransaction.apply(jsonObjectMapper, msg).orElse(null);
            CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(aggregatedTransaction).orElse(null);
            if (aggregatedTransaction != null && ccReconHeader != null) {
                scheduleMessageRetryCount = ccReconHeader.getMessageEventRescheduleCount();

                outputsResponse = testComponent.processEvent(new StreamInputRequest(1, 1, INCOMING_REC_ACCT_Q, List.of(EXCEPTION_Q, OUTGOING_SUCCESS_Q), aggregatedTransaction));
            }
            else {
                fail("AggregatedTransaction or CcReconHeader is null");
                break;
            }
        }
        while (scheduleMessageRetryCount < appConfig.getMessageRetryCountLimit());

        Assertions.assertTrue(output.getAll().contains("Exhausted '" + appConfig.getMessageRetryCountLimit() + "' retries for Global ID: " + aggregatedGeneralLedgerTransactionDetails.getGlobalId()));
        assertThat(output.getAll(), containsString("Retries exhausted, exception code: " + GLPOSTING_FAILEDCONNECTION.name()));

        byte[] exceptionMessage = (byte[]) outputsResponse.getOutputs().get(EXCEPTION_Q + "0");
        CcReconException ex = TestModelExtractor.getCcReconException.apply(messageObjectMapper, exceptionMessage).orElse(null);
        if (ex != null) {
            assertThat(ex.getExceptionCode(), is(GLPOSTING_FAILEDCONNECTION.name()));
            assertThat(ex.getExceptionStatus(), is("OPEN"));
            assertThat(ex.getExceptionUUID(), notNullValue());
            assertThat(ex.getCreatedDate(), notNullValue());
            assertThat(ex.getExceptionSource(), is(APP_NAME));
            assertThat(ex.getGlobalId(), is(aggregatedGeneralLedgerTransactionDetails.getGlobalId()));
        }
        else {
            fail("CcReconException is null");
        }
    }
}
