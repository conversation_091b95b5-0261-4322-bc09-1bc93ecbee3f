package com.aa.ccrecon.gl;

import com.itfacs.pmt.controller.StreamController;
import com.itfacs.pmt.controller.StreamInputRequest;
import com.itfacs.pmt.controller.StreamOutputsResponse;
import org.springframework.cloud.function.context.FunctionCatalog;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class TestComponent extends StreamController {

    public TestComponent(final FunctionCatalog functions) {
        super(functions);
    }

    public StreamOutputsResponse processEvent(StreamInputRequest req) throws IOException {
        return super.process(req);
    }
}
