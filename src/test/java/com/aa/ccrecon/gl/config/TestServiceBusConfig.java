package com.aa.ccrecon.gl.config;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import reactor.core.publisher.Sinks;

@TestConfiguration
public class TestServiceBusConfig {
    @Bean("serviceBusAggregatedGeneralLedgerTransactionDetailsSink")
    public Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedGeneralLedgerTransactionDetailsSink() {
        return Sinks.many().multicast().onBackpressureBuffer();
    }

    @Bean("sendExceptionMessageSink")
    public Sinks.Many<Message<AggregatedTransaction>> sendExceptionMessageSink() {
        return Sinks.many().multicast().onBackpressureBuffer();
    }
}
