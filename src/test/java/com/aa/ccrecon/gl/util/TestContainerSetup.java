package com.aa.ccrecon.gl.util;

import org.jetbrains.annotations.NotNull;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;
import org.testcontainers.utility.MountableFile;
@SuppressWarnings("resource")
public abstract class TestContainerSetup {

    static int testPort = 6379;

    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:5.0.3-alpine"))
            .withExposedPorts(testPort);

    static PostgreSQLContainer<?> ipsPostgres;

    static DockerImageName POSTGRES_TEST_IMAGE = DockerImageName.parse("postgres:15.4");


    static {

        ipsPostgres  = new PostgreSQLContainer<>(POSTGRES_TEST_IMAGE)
                .withDatabaseName("ccrecon")
                .withCopyFileToContainer(
                        MountableFile.forClasspathResource("/sql/ccrecon.sql"),
                        "/docker-entrypoint-initdb.d/1schema.sql"
                )
                .withPassword("test")
                .withUsername("test");
                

        ipsPostgres.start();
        redis.start();

    }


    @DynamicPropertySource
    static void setProperties(@NotNull DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.azure.compatibility-verifier.enabled:", () -> false);
        registry.add("spring.datasource.url", ipsPostgres::getJdbcUrl);
        registry.add("spring.datasource.username", ipsPostgres::getUsername);
        registry.add("spring.datasource.password", ipsPostgres::getPassword);

        registry.add("retry.times",() -> "2");
        registry.add("retry.delay",() -> "1");

        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", ()-> redis.getMappedPort(testPort).toString());
        registry.add("spring.data.redis.password",() -> "");
    }
}
