package com.aa.ccrecon.gl.util;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageEnvelope;
import com.aa.ccrecon.domain.aggregation.Payload;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.gl.entity.ReceivableAccountingTransDetailsEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.model.request.Body;
import com.aa.ccrecon.gl.model.request.JournalDocument;
import com.aa.ccrecon.gl.model.request.JournalEntryItem;
import com.aa.ccrecon.gl.model.request.JournalEntryItemData;
import com.aa.ccrecon.gl.model.request.PostGeneralLedgerRq;
import com.aa.ccrecon.gl.model.request.PostGeneralLedgerRqEnvelope;
import com.aa.ccrecon.gl.model.request.SapRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TestModelExtractor {

    private TestModelExtractor() {
    }

    public static final Function<AggregatedGeneralLedgerTransactionDetails, AggregatedTransaction> createAggregateTransaction = aggregatedGeneralLedgerTransactionDetails -> {
        AggregatedTransaction aggregatedTransaction = new AggregatedTransaction();
        MessageEnvelope messageEnvelope = new MessageEnvelope();
        Payload payload = new Payload();

        payload.setCcReconHeader(new CcReconHeader());
        payload.setAggregatedGeneralLedgerTransactionDetails(aggregatedGeneralLedgerTransactionDetails);
        messageEnvelope.setPayload(payload);
        aggregatedTransaction.setMessageEnvelope(messageEnvelope);

        return aggregatedTransaction;
    };

    public static BiFunction<ObjectMapper, byte[], Optional<AggregatedTransaction>> getAggregatedTransaction = (objectMapper, message) -> getMessagePayload(AggregatedTransaction.class).apply(objectMapper, message);

    private static final BiFunction<ObjectMapper, byte[], Optional<Payload>> getAggregatedTransactionPayload = (objectMapper, message) -> getAggregatedTransaction.apply(objectMapper, message)
            .map(AggregatedTransaction::getMessageEnvelope)
            .map(MessageEnvelope::getPayload);

    public static BiFunction<ObjectMapper, byte[], Optional<CcReconException>> getCcReconException = (objectMapper, message) -> getAggregatedTransactionPayload.apply(objectMapper, message)
            .map(Payload::getCcReconHeader)
            .map(CcReconHeader::getCcReconExceptions)
            .map(List::stream)
            .flatMap(Stream::findFirst);

    public static BiFunction<ObjectMapper, byte[], Optional<String>> getGlobalId = (objectMapper, message) -> getAggregatedTransactionPayload.apply(objectMapper, message)
            .map(Payload::getAggregatedGeneralLedgerTransactionDetails)
            .map(AggregatedGeneralLedgerTransactionDetails::getGlobalId);

    public static <T> BiFunction<ObjectMapper, byte[], Optional<T>> getMessagePayload(Class<T> clazz) {
        return (objectMapper, message) -> Optional.ofNullable(message)
                .map(payload -> Optional.ofNullable(objectMapper)
                        .map(mapper -> {
                            try {
                                return mapper.readValue(payload, clazz);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }))
                .orElse(null);
    }

    public static Function<SapRequest, JournalDocument> getJournalDocument = sapRequest ->
            Optional.ofNullable(sapRequest)
                    .map(SapRequest::getPostGeneralLedgerRqEnvelope)
                    .map(PostGeneralLedgerRqEnvelope::getBody)
                    .map(Body::getPostGeneralLedgerRq)
                    .map(PostGeneralLedgerRq::getJournalDocument)
                    .orElse(null);

    public static Function<UatpAggregateReceivableAccountingTransEntity, Long> getDetailId = uatpAggregateReceivableAccountingTransEntity ->
            Optional.ofNullable(uatpAggregateReceivableAccountingTransEntity)
                    .map(UatpAggregateReceivableAccountingTransEntity::getReceivableAccountingTransDetailsEntity)
                    .map(ReceivableAccountingTransDetailsEntity::getDetailId)
                    .orElse(null);

    public static Function<SapRequest, List<JournalEntryItemData>> getJournalEntryItemData = sapRequest -> Optional.of(sapRequest)
            .map(SapRequest::getPostGeneralLedgerRqEnvelope)
            .map(PostGeneralLedgerRqEnvelope::getBody)
            .map(Body::getPostGeneralLedgerRq)
            .map(PostGeneralLedgerRq::getJournalDocument)
            .map(JournalDocument::getJournalEntryItems)
            .map(items -> items.stream()
                    .map(JournalEntryItem::getJournalEntryItemData)
                    .collect(Collectors.toList()))
            .orElse(new ArrayList<>());
}
