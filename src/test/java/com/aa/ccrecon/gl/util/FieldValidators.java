package com.aa.ccrecon.gl.util;

import static org.junit.Assert.assertTrue;

import java.util.Set;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.aa.ccrecon.gl.model.request.JournalEntryItemData;
import com.aa.ccrecon.gl.model.request.RoutingInfo;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

public class FieldValidators
{
    static private ValidatorFactory factory;
    static private Validator validator;

    @BeforeAll
    public static void setup(){
        factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    public void TestRoutingInfo(){
        JournalEntryItemData entryItem = new JournalEntryItemData(null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
        entryItem.setDocType("AB");
        entryItem.setCompanyCode("ABCD");
        entryItem.setCurrency("USD");
        entryItem.setDocumentHeaderText("12365478963258741236lkjhg");
        entryItem.setReferenceDocumentnumber("kdlsiejsldksjdlf");
        entryItem.setAccountNumber("kdiclshend");
        entryItem.setAmountInDocumentCurrency("*************.12");
        entryItem.setAssignmentNumber("123456789632587412");
        entryItem.setItemText("jL2E4kFmN3oPyQsRbWgX6hT7vYuZtAaS5cD8eV1iG0jKlMqOwR");

        Set<ConstraintViolation<JournalEntryItemData>> violations = validator.validate(entryItem);
        
        assertTrue(violations.isEmpty());
    }

    @Test
    public void TestJournalEntryItemValidator(){
        RoutingInfo routingInfo = new RoutingInfo(null, null, null, null, null, null, null, null, null, null, null);

        routingInfo.setDomain("asdfasdfasdfasd");
        routingInfo.setInterFace("asdfasdfasdfasdfasdfasdfasdfas");
        routingInfo.setSource("asdfa");
        routingInfo.setDestination("asdfa");
        routingInfo.setService("asdfasdfas");
        routingInfo.setTransType("asdfa");
        routingInfo.setSapDocType("asdfa");
        routingInfo.setUserId("jL2E4kFmN3oPyQsRbWgX6hT7vYuZtAaS5cD8eV1iG0jKlMqOwR");
        routingInfo.setGlobalUniqueId("jL2E4kFmN3oPyQsRbWgX6hT7vYuZtAaS5cD8eV1iG0jKlMqOwR");

        Set<ConstraintViolation<RoutingInfo>> violations = validator.validate(routingInfo);
        System.out.println(violations);
        assertTrue(violations.isEmpty());
    }


}
