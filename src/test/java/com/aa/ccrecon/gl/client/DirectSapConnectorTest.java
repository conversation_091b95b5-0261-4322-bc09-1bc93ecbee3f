package com.aa.ccrecon.gl.client;

import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.github.tomakehurst.wiremock.client.WireMock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;
import org.wiremock.spring.EnableWireMock;

import static com.github.tomakehurst.wiremock.client.WireMock.jsonResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.isA;
import static org.junit.Assert.assertThrows;

@SpringBootTest(classes =  {DirectSapConnector.class})
@ActiveProfiles("test")
@Import({DirectSapConnectorTest.TestConfig.class})
@EnableWireMock
class DirectSapConnectorTest {
    @Autowired
    private DirectSapConnector sapConnector;
//    @Autowired
//    private OAuth2AuthorizedClientManager authorizedClientManager;


    @Value("${wiremock.server.baseUrl}")
    private String wiremockBaseUrl;

    @BeforeEach
    void init() {
        WireMock.reset();
//        OAuth2AuthorizedClient mockClient = mock(OAuth2AuthorizedClient.class);
//        OAuth2AccessToken mockToken = mock(OAuth2AccessToken.class);
//
//        when(mockToken.getTokenValue()).thenReturn("my-token");
//        when(mockClient.getAccessToken()).thenReturn(mockToken);
//
//        when(authorizedClientManager.authorize(any())).thenReturn(mockClient);
    }

    @Test
    public void testGetResponse_WithException() {
        stubFor(post("/postJournalEntry")
                .willReturn(jsonResponse("{\"postingStatus\":\"Failure\"}", 500)));

        GeneralLedgerEnvelope request = new GeneralLedgerEnvelope();

        assertThrows(SapResponseException.class, () -> sapConnector.postToSap(request));
    }

    @Test
    public void testPostToSap_Success() throws SapResponseException {
        stubFor(post("/postJournalEntry")
                .willReturn(okJson("{\"postingStatus\":\"Success\"}")));

        GeneralLedgerEnvelope request = new GeneralLedgerEnvelope();

        var result = sapConnector.postToSap(request);

        assertThat(result, isA(GlPostingResponse.class));
    }

//    @Test
//    public void testPostToSap_401Then_NewToken_ThenOk() throws SapResponseException {
//        stubFor(post("/postJournalEntry").withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer my-token"))
//                .inScenario("token-refresh")
//                .whenScenarioStateIs("Started")
//                .willReturn(jsonResponse("{\"postingStatus\":\"Failure\"}", 401))
//                .willSetStateTo("token-expired"));
//        stubFor(post("/postJournalEntry").withHeader(HttpHeaders.AUTHORIZATION, matching("Bearer my-new-token"))
//                .inScenario("token-refresh")
//                .whenScenarioStateIs("token-expired")
//                .willReturn(okJson("{\"postingStatus\":\"Success\"}")));
//
//        OAuth2AuthorizedClient firstClient = mock(OAuth2AuthorizedClient.class);
//        OAuth2AccessToken mockToken = mock(OAuth2AccessToken.class);
//        when(mockToken.getTokenValue()).thenReturn("my-token");
//        when(firstClient.getAccessToken()).thenReturn(mockToken);
//
//        OAuth2AuthorizedClient renewedClient = mock(OAuth2AuthorizedClient.class);
//        OAuth2AccessToken renewedToken = mock(OAuth2AccessToken.class);
//        when(renewedToken.getTokenValue()).thenReturn("my-new-token");
//        when(renewedClient.getAccessToken()).thenReturn(renewedToken);
//
//        when(authorizedClientManager.authorize(any()))
//                .thenReturn(firstClient)
//                .thenReturn(renewedClient);
//
//        GeneralLedgerEnvelope request = new GeneralLedgerEnvelope();
//
//        var result = sapConnector.postToSap(request);
//
//        assertThat(result, isA(GlPostingResponse.class));
//    }


    @TestConfiguration
    static class TestConfig {

        @Bean
        @Primary
        public SapRequestConfig testSapReqConfig(@Value("${wiremock.server.baseUrl}") String wiremockBaseUrl) {
            return new SapRequestConfig() {
                @Override
                public String getUrl() {
                    return wiremockBaseUrl;
                }
            };
        }

        @Bean
        @Primary
        public WebClient SAPWebClient(@Value("${wiremock.server.baseUrl}") String wiremockBaseUrl) {
            return WebClient.builder()
                    .baseUrl(wiremockBaseUrl)
                    .build();
        }
    }
}