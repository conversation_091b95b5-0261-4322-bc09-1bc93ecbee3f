package com.aa.ccrecon.gl.service.documentheader;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageEnvelope;
import com.aa.ccrecon.domain.aggregation.Payload;
import com.aa.ccrecon.domain.subledger.TransactionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.REFUND_TEXT;
import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.SALE_TEXT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

class DocumentHeaderTextLocatorTest {
    private DocumentHeaderTextLocator docHeaderTextLocator;
    private AggregatedGeneralLedgerTransactionDetails details;
    private AggregatedTransaction message;

    @BeforeEach
    void setup() {
        docHeaderTextLocator = new DocumentHeaderTextLocator();

        details = new AggregatedGeneralLedgerTransactionDetails();
        Payload payload = new Payload();
        payload.setAggregatedGeneralLedgerTransactionDetails(details);
        MessageEnvelope envelope = new MessageEnvelope();
        envelope.setPayload(payload);
        message = new AggregatedTransaction();
        message.setMessageEnvelope(envelope);
    }

    @Test
    void givenRefundMessage_WhenRetrievingDocHeaderText_ThenReturnRefundText() {
        details.setTransactionType(TransactionType.TYPES.REFUND);

        final String result = docHeaderTextLocator.getDocHeaderText(message);

        assertThat(result, is(REFUND_TEXT));
    }

    @Test
    void givenSaleMessage_WhenRetrievingDocHeaderText_ThenReturnSaleText() {
        details.setTransactionType(TransactionType.TYPES.SALE);

        final String result = docHeaderTextLocator.getDocHeaderText(message);

        assertThat(result, is(SALE_TEXT));
    }
}