package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.config.AppConfig;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingTransDetailsEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.mapper.RecAccGLPostingMapperImpl;
import com.aa.ccrecon.gl.mapper.RecAccGlSummaryMapperImpl;
import com.aa.ccrecon.gl.message.MessageQueueProcessor;
import com.aa.ccrecon.gl.model.GlResponseEnum;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.repo.AccountingStatusEntityRepository;
import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLPostingRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLSummaryRepository;
import com.aa.ccrecon.gl.repo.UatpAggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.service.aggregategl.AggregateReceivableAccountingTransFactory;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import static com.aa.ccrecon.domain.aggregation.MessageType.AGGREGATED_GL_NON_UATP;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {ReturnStatusService.class, AggregateReceivableAccountingTransFactory.class, RecAccGLPostingMapperImpl.class,
        RecAccGlSummaryMapperImpl.class, RetryService.class})
public class ReturnStatusServiceTests {

    @MockitoBean
    private ReceivableAccountingGLPostingRepository receivableAccountingGLPostingRepository;
	@MockitoBean
    private ReceivableAccountingGLSummaryRepository receivableAccountingGLSummaryRepository;
	@MockitoBean
    private AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo;
    @MockitoBean
    private AccountingStatusEntityRepository accountingStatusEntityRepository;
    @MockitoBean
    private MessageQueueProcessor messageQueueProcessor;
    @MockitoBean
    private AggregateReceivableAccountingTransFactory aggregateReceivableAccountingTransFactory;
    @Autowired
    private ReturnStatusService service;
    @MockitoBean
    private UatpAggregateReceivableAccountingTransRepo uatpAggregateReceivableAccountingTransRepo;
    @MockitoBean
    private AppConfig appConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @DisplayName("Given an SAP Exception, when attempting to store the details, then Details entities are saved and retry message is sent")
    public void testStore5xxErrorResults() throws IOException {
        String globalId = "123";
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", globalId, 1, AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);

        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        GeneralLedgerEnvelope sapRequest = createSapRequest();
        SapResponseException sapResponseException = new SapResponseException("{}", "message", 500);

        AggregateReceivableAccountingTransEntity entity = new AggregateReceivableAccountingTransEntity();
        entity.setAggregateId("1L");
        AggregateReceivableAccountingTransEntity entity2 = new AggregateReceivableAccountingTransEntity();
        entity2.setAggregateId("2L");

        List list = List.of(entity, entity2);

        when(aggregateReceivableAccountingTransFactory.findByGlobalId(eq(globalId), eq(AGGREGATED_GL_NON_UATP)))
                .thenReturn(list);

        var details1 = createReceivableAccountingTransDetailsEntity();
        entity.setReceivableAccountingTransDetailsEntities(Set.of(details1));
        var details2 = createReceivableAccountingTransDetailsEntity();
        entity2.setReceivableAccountingTransDetailsEntities(Set.of(details2));

        service.storeErrorAndRetry(aggregatedTransaction, aggregatedGeneralLedgerTransactionDetails, sapRequest,
                sapResponseException, AGGREGATED_GL_NON_UATP);

        verify(messageQueueProcessor, times(1)).retryTransactionRequest(eq(aggregatedTransaction));
        verify(receivableAccountingGLPostingRepository, times(2)).save(any(ReceivableAccountingGLEntity.class));

        verify(accountingStatusEntityRepository, times(1))
                .insertAccountingStatusNonUatp(eq(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.name()), eq(globalId));

    }

    @DisplayName("Given a standard SAP error, when storing the results, then the correct number of records are saved")
    @ValueSource(strings = { "sapresponse_duplicate.json", "sapresponse_error.json" })
    @ParameterizedTest
    void testStoreErrorResults(String fileName) throws IOException {
        String globalId = "123";
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", globalId, 1, AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);

        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        GeneralLedgerEnvelope sapRequest = createSapRequest();
        GlPostingResponse sapResponse = createSapResponse(fileName);

        AggregateReceivableAccountingTransEntity entity = new AggregateReceivableAccountingTransEntity();
        entity.setAggregateId("1L");
        AggregateReceivableAccountingTransEntity entity2 = new AggregateReceivableAccountingTransEntity();
        entity2.setAggregateId("2L");

        List list = List.of(entity, entity2);

        when(aggregateReceivableAccountingTransFactory.findByGlobalId(eq(globalId), eq(AGGREGATED_GL_NON_UATP)))
                .thenReturn(list);

        var details1 = createReceivableAccountingTransDetailsEntity();
        entity.setReceivableAccountingTransDetailsEntities(Set.of(details1));
        var details2 = createReceivableAccountingTransDetailsEntity();
        entity2.setReceivableAccountingTransDetailsEntities(Set.of(details2));

        service.storeErrorResults(aggregatedTransaction, aggregatedGeneralLedgerTransactionDetails, sapRequest,
                sapResponse, AGGREGATED_GL_NON_UATP);

        verifyNoInteractions(messageQueueProcessor);
        verify(receivableAccountingGLPostingRepository, times(2)).save(any(ReceivableAccountingGLEntity.class));

        if (fileName.equals("sapresponse_duplicate.json")) {
            verify(accountingStatusEntityRepository, times(1))
                    .insertAccountingStatusNonUatp(eq(GlResponseEnum.GLPOSTING_DUPLICATE.name()), eq(globalId));
        } else {
            verify(accountingStatusEntityRepository, times(1))
                    .insertAccountingStatusNonUatp(eq(GlResponseEnum.GLPOSTING_ERROR.name()), eq(globalId));
        }
    }

    private ReceivableAccountingTransDetailsEntity createReceivableAccountingTransDetailsEntity() {
        ReceivableAccountingTransDetailsEntity entity = new ReceivableAccountingTransDetailsEntity();
        entity.setIssueDate(LocalDate.now());
        entity.setDocumentUuid(UUID.randomUUID().toString());
        entity.setPnr("PNR");
        entity.setTicketNumber("TICKETNUMBER");
        entity.setCurrency("EUR");
        entity.setAmount(BigDecimal.valueOf(12.12));
        entity.setClientCaptureReceivedId(UUID.randomUUID());
        entity.setPspReference("PSP_REFERNECE");
        entity.setCardBin("123456");
        entity.setCardLastFourDigits("4321");
        entity.setRefundId("REF123");
        entity.setTransactionType("SALE");
        entity.setDetailId(1L);
        return entity;
    }

    private GeneralLedgerEnvelope createSapRequest() throws IOException {
        InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/sap_request_basic_single.json")));
        return objectMapper.readValue(isr, GeneralLedgerEnvelope.class);
    }

    private GlPostingResponse createSapResponse(String fileName) throws IOException {
        InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/" + fileName)));
        return objectMapper.readValue(isr, GlPostingResponse.class);
    }
}
