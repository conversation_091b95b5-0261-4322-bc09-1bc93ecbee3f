package com.aa.ccrecon.gl.service.doctype;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageEnvelope;
import com.aa.ccrecon.domain.aggregation.Payload;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

class DocTypeLocatorTest {
    private DocTypeLocator docTypeLocator;
    private AggregatedGeneralLedgerTransactionDetails details;
    private AggregatedTransaction message;

    @BeforeEach
    void setup() {
        docTypeLocator = new DocTypeLocator();

        details = new AggregatedGeneralLedgerTransactionDetails();
        Payload payload = new Payload();
        payload.setAggregatedGeneralLedgerTransactionDetails(details);
        MessageEnvelope envelope = new MessageEnvelope();
        envelope.setPayload(payload);
        message = new AggregatedTransaction();
        message.setMessageEnvelope(envelope);
    }

    @Test
    void givenRefundMessage_WhenRetrievingDocType_ThenReturnR9() {
        details.setTransactionType(TransactionType.TYPES.REFUND);

        DocType result = docTypeLocator.getDocType(message);

        assertThat(result, is(DocType.R9));
    }

    @Test
    void givenSaleMessageCompanyLocal_WhenRetrievingDocType_ThenReturnE9() {
        details.setTransactionType(TransactionType.TYPES.SALE);
        details.setSalesSource(SalesSource.COMPANY_LOCAL);

        DocType result = docTypeLocator.getDocType(message);

        assertThat(result, is(DocType.E9));
    }

    // this will change later
    @Test
    void givenSaleMessageNotCompanyLocal_WhenRetrievingDocType_ThenReturnE9() {
        details.setTransactionType(TransactionType.TYPES.SALE);
        details.setSalesSource(SalesSource.AGENCY_ARC);

        DocType result = docTypeLocator.getDocType(message);

        assertThat(result, is(DocType.E9));
    }
}