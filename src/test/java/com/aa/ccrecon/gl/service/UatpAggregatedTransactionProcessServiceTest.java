package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.config.WebClientConfig;
import com.aa.ccrecon.gl.entity.AccountingStatusEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.mapper.SapRequestMapper;
import com.aa.ccrecon.gl.model.GlResponseEnum;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.request.PostJournal;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.repo.AccountingStatusEntityRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLPostingRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLSummaryRepository;
import com.aa.ccrecon.gl.repo.UatpAggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.service.aggregategl.UatpAggregatedTransactionProcessService;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.aa.ccrecon.gl.utils.AppConstants;
import com.aa.ccrecon.gl.validation.ValidationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.aa.ccrecon.domain.constants.DocType.E9;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest
@ActiveProfiles("test")
@ExtendWith(OutputCaptureExtension.class)
public class UatpAggregatedTransactionProcessServiceTest extends TestContainerSetup {

    @MockitoBean
    WebClient webClient;

    @MockitoBean
    WebClientConfig webClientConfig;

    @MockitoBean
    DirectSapConnector directSapConnector;

    @Autowired
    UatpAggregateReceivableAccountingTransRepo repo;

    @Autowired
    UatpAggregatedTransactionProcessService uatpAggregatedTransactionProcessService;

    @Autowired
    ReceivableAccountingGLPostingRepository receivableAccountingGLPostingRepository;

    @MockitoSpyBean
    ReceivableAccountingGLSummaryRepository receivableAccountingGLSummaryRepository;

    @Autowired
    AccountingStatusEntityRepository accountingStatusEntityRepository;

    @MockitoBean
    DirectSapConnector sapConnector;

    @MockitoBean
    ValidationService validationService;

    @MockitoSpyBean
    private SapRequestMapper sapRequestMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @ParameterizedTest
    @EnumSource
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpAggregatedGeneralLedgerTransactionDetails_WhenProcessed_VerifyLogsAndDatabase(DocType docType, CapturedOutput capturedOutput) throws SapResponseException, IOException {

        InputStreamReader r = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/sapresponse_uatp_success.json")));
        GlPostingResponse sapResponse = objectMapper.readValue(r, GlPostingResponse.class);

        ArgumentCaptor<GeneralLedgerEnvelope> reqCaptor = ArgumentCaptor.forClass(GeneralLedgerEnvelope.class);
        when(sapConnector.postToSap(reqCaptor.capture())).thenReturn(sapResponse);

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("UATP-3");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);
        aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.SALE);

        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        uatpAggregatedTransactionProcessService.process(aggregatedTransaction, docType, "test header text");

        List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findByGlobalId(aggregatedGeneralLedgerTransactionDetails.getGlobalId());
//        List<ReceivableAccountingGLSummaryEntity> summaries = receivableAccountingGLSummaryRepository.findAll().stream()
//                .filter(i -> i.getGlobalId().equals(aggregatedGeneralLedgerTransactionDetails.getGlobalId()))
//                .toList();
        List<AccountingStatusEntity> accountingStatusEntities = accountingStatusEntityRepository.findAll();

        assertTrue(capturedOutput.getOut().contains("Number of records found: 11"));
        assertEquals(1, postings.size());
        assertThat(postings.getFirst().getTransactionType(), is("SALE"));
//        assertEquals(1, summaries.size());
//        assertThat(summaries.getFirst().getTransactionType(), is("SALE"));

        // Verify AccountingStatusEntity new fields
        assertFalse(accountingStatusEntities.isEmpty());
        AccountingStatusEntity accountingStatus = accountingStatusEntities.getFirst();
        assertEquals("123456", accountingStatus.getCardBin());
        assertEquals("4321", accountingStatus.getCardLastFourDigits());
        assertEquals("REF123", accountingStatus.getRefundId());
        assertEquals("SALE", accountingStatus.getTransactionType());

        GeneralLedgerEnvelope req = reqCaptor.getValue();
        PostJournal journal = req.getPostJournal();
        assertThat(journal.getRoutingInfo().getSapDocType(), is(docType.name()));
        assertThat(journal.getPostJournalDetail().getFirst().getDocType(), is(docType.name()));
        assertThat(journal.getPostJournalDetail().getFirst().getHeaderTxt(), is("test header text"));

        verify(sapRequestMapper, times(1)).mapGlEnvelopeUatp(
                anyList(), eq(aggregatedGeneralLedgerTransactionDetails), any(SapRequestConfig.class)
        );
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpAggregatedGeneralLedgerTransactionDetails_WhenSapResponseErrors_VerifyGLPostingRepositoryEntries(CapturedOutput capturedOutput) throws SapResponseException {

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("UATP-3");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);
        aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.SALE);

        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        SapResponseException exception = Mockito.mock(SapResponseException.class);
        when(exception.getBody()).thenReturn("Error");
        when(exception.getMessage()).thenReturn("Error Message");
        when(exception.getStatusCode()).thenReturn(500);

        when(sapConnector.postToSap(any(GeneralLedgerEnvelope.class)))
                .thenThrow(exception);

        uatpAggregatedTransactionProcessService.process(aggregatedTransaction, E9, "test header text");

        List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findByGlobalId(aggregatedGeneralLedgerTransactionDetails.getGlobalId());
        List<AccountingStatusEntity> entities = accountingStatusEntityRepository.findAll();

        assertEquals(1, postings.size());
        assertTrue(postings.stream().allMatch(i ->
                i.getGlPostingStatus().equals(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString()) &&
                        i.getTransactionType().equals(TransactionType.TYPES.SALE.toString())
        ));
        assertTrue(capturedOutput.getOut().contains("Number of records found: 11"));
        assertEquals(10, entities.size());
        assertTrue(entities.stream().allMatch(i -> i.getStatus().equals(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString())));

        //verify new fields persist
        AccountingStatusEntity accountingStatus = entities.getFirst();
        assertEquals("123456", accountingStatus.getCardBin());
        assertEquals("4321", accountingStatus.getCardLastFourDigits());
        assertEquals("REF123", accountingStatus.getRefundId());
        assertEquals("SALE", accountingStatus.getTransactionType());

        verifyNoInteractions(receivableAccountingGLSummaryRepository);
    }

    @Test
    @Sql(value = "/sql/1549240generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpAggregatedGeneralLedgerTransactionDetails_WhenSapResponseErrors_VerifyAccountingStatusEntries() throws SapResponseException {

        SapResponseException exception = Mockito.mock(SapResponseException.class);
        when(exception.getBody()).thenReturn("Error");
        when(exception.getMessage()).thenReturn("Error Message");
        when(exception.getStatusCode()).thenReturn(500);

        when(sapConnector.postToSap(any(GeneralLedgerEnvelope.class)))
                .thenThrow(exception);

        String[] globalIds = {"UATP-4107", "UATP-4108", "UATP-4109"};
        for (String globalId : globalIds) {
            AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
            aggregatedGeneralLedgerTransactionDetails.setGlobalId(globalId);
            aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);
            aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.REFUND);

            AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
            uatpAggregatedTransactionProcessService.process(aggregatedTransaction, E9, "test header text");
        }

        List<AccountingStatusEntity> entities = accountingStatusEntityRepository.findAll();

        assertEquals(7, entities.size());

        Set<String> pnrNumbers = new HashSet<>();
        entities.forEach(i -> {
            assertEquals(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString(), i.getStatus());
            assertEquals("REFUND", i.getTransactionType());
            assertTrue(pnrNumbers.add(i.getPnrNumber()), "Duplicate PNR number found: " + i.getPnrNumber());
        });

        verifyNoInteractions(receivableAccountingGLSummaryRepository);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpAggregateTransactionData_whenFilteringOnGlobalId_validateJoinWithTransDetails() {
        List<UatpAggregateReceivableAccountingTransEntity> list = repo.findByGlobalIdOrderByCurrencyAsc("UATP-3");
        list.stream().filter(Objects::nonNull).forEach(entity -> {
            assertEquals("UATP-3", entity.getGlobalId());
            switch (entity.getRecordType()) {
                case AppConstants.RecordType.DETAIL:
                    assertNotNull(entity.getReceivableAccountingTransDetailsEntity());
                    break;
                case AppConstants.RecordType.AGGREGATE:
                    assertNull(entity.getReceivableAccountingTransDetailsEntity());
                    break;
                default:
                    fail("Invalid Record Type");
            }
        });

        assertEquals(11, list.size());
    }

    @ValueSource(strings = {"sapresponse_duplicate.json", "sapresponse_error.json"})
    @ParameterizedTest
    @DisplayName("Given UATP SAP request, when SAP returns an error, then verify GL posting repository entries are created with correct status")
    @Sql(value = "/sql/1549240generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void sapError_NotConnectionIssue(String fileName) throws SapResponseException, IOException {

        InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/" + fileName)));
        GlPostingResponse response = objectMapper.readValue(isr, GlPostingResponse.class);

        when(sapConnector.postToSap(any(GeneralLedgerEnvelope.class)))
                .thenReturn(response);

        String[] globalIds = {"UATP-4107", "UATP-4108", "UATP-4109"};
        for (String globalId : globalIds) {
            AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
            aggregatedGeneralLedgerTransactionDetails.setGlobalId(globalId);
            aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);
            aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.REFUND);

            AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
            uatpAggregatedTransactionProcessService.process(aggregatedTransaction, E9, "test header text");
        }

        List<AccountingStatusEntity> entities = accountingStatusEntityRepository.findAll();

        assertEquals(7, entities.size());

        Set<String> pnrNumbers = new HashSet<>();
        entities.forEach(i -> {
            if (fileName.equals("sapresponse_duplicate.json")) {
                assertEquals(i.getStatus(), GlResponseEnum.GLPOSTING_DUPLICATE.toString());
            } else {
                assertEquals(i.getStatus(), GlResponseEnum.GLPOSTING_ERROR.toString());
            }
            assertEquals("REFUND", i.getTransactionType());
            assertTrue(pnrNumbers.add(i.getPnrNumber()), "Duplicate PNR number found: " + i.getPnrNumber());
        });

        verifyNoInteractions(receivableAccountingGLSummaryRepository);

    }

}
