package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.service.doctype.DocTypeService;
import com.aa.ccrecon.gl.service.documentheader.DocHeaderService;
import com.aa.ccrecon.gl.service.settlement.TransactionTypeMappingService;
import com.aa.ccrecon.gl.service.svclocator.SapPostingInterface;
import com.aa.ccrecon.gl.service.svclocator.SapSvcFactory;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import static com.aa.ccrecon.domain.aggregation.AccountingType.SETTLEMENT_ACCOUNTING;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith({MockitoExtension.class, OutputCaptureExtension.class})
class MessageProcessorServiceTest {
    @Mock
    private SapSvcFactory sapSvcFactory;
    @Mock
    private DocTypeService docTypeService;
    @Mock
    private DocHeaderService docHeaderService;
    @Mock
    private TransactionTypeMappingService transactionTypeMappingService;
    @InjectMocks
    private MessageProcessorService messageProcessorService;

    @Test
    @DisplayName("Given a receivable accounting message, when the message is processed, " +
            "then the sapSvcFactory returns a posting interface")
    void process_rec_acct() {
        var mockInterface = mock(SapPostingInterface.class);
        when(sapSvcFactory.getPostingInterface(anyString())).thenReturn(mockInterface);
        when(docTypeService.getDocType(any(AggregatedTransaction.class))).thenReturn(DocType.E9);
        when(docHeaderService.getDocHeaderText(any(AggregatedTransaction.class))).thenReturn("docHeaderText");

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction trans = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        messageProcessorService.processReceivableAccounting(trans);

        verify(mockInterface, times(1)).process(eq(trans), eq(DocType.E9), eq("docHeaderText"));
    }

    @Test
    @DisplayName("Given a settlement message, when the message is processed, " +
            "then the message is logged")
    void process_settlement_acct(CapturedOutput logs) {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, SETTLEMENT_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction trans = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        messageProcessorService.processSettlementAccounting(trans);

        verifyNoInteractions(sapSvcFactory);
        verifyNoInteractions(docTypeService);
        verifyNoInteractions(docHeaderService);
        verify(transactionTypeMappingService, times(1)).logTransactionTypeMapping(eq(trans));

        assertThat(logs.getAll(), containsString("Processing settlement accounting message"));
    }

    @Test
    @DisplayName("Given a settlement message with non-settlement accounting type, when the message is processed, " +
            "then transaction type mapping is not called")
    void process_settlement_acct_non_settlement_type(CapturedOutput logs) {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction trans = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        messageProcessorService.processSettlementAccounting(trans);

        verifyNoInteractions(sapSvcFactory);
        verifyNoInteractions(docTypeService);
        verifyNoInteractions(docHeaderService);
        verifyNoInteractions(transactionTypeMappingService);

        assertThat(logs.getAll(), containsString("Processing settlement accounting message"));
    }
}