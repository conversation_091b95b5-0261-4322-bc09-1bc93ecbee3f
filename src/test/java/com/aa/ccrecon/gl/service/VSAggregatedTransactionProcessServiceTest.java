package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.client.VirtualServiceSapConnector;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.service.aggregategl.AggregatedTransactionProcessService;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.aa.ccrecon.gl.validation.ValidationService;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.jdbc.Sql;

import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.SALE_TEXT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest
@ActiveProfiles({"virtual-service", "test"})
class VSAggregatedTransactionProcessServiceTest extends TestContainerSetup {
	@Autowired
	AggregatedTransactionProcessService aggregatedTransactionProcessService;

	@MockitoBean
	ValidationService validationService;

	@MockitoSpyBean
	private VirtualServiceSapConnector vsSapConnector;

    @MockitoBean
    DirectSapConnector directSapConnector;

	@ParameterizedTest
	@EnumSource
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	void givenSAPGLRequest_WhenProfileIsVirtualService_ThenVerifyGLEntityReturn(DocType docType) {
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
				null, "1", 0, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		aggregatedTransactionProcessService.process(aggregatedTransaction, docType, SALE_TEXT);

		verify(vsSapConnector, times(1)).postToSap(any(GeneralLedgerEnvelope.class));

	}
}