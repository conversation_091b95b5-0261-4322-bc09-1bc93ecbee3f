package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.client.VirtualServiceSapConnector;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.service.aggregategl.UatpAggregatedTransactionProcessService;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.jdbc.Sql;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest
@ActiveProfiles({"virtual-service", "test"})
public class VSUatpTransactionProcessServiceTest extends TestContainerSetup {
	@Autowired
	UatpAggregatedTransactionProcessService uatpAggregatedTransactionProcessService;

	@MockitoSpyBean
	private VirtualServiceSapConnector vsSapConnector;

    @MockitoBean
    DirectSapConnector directSapConnector;

	@ParameterizedTest
	@EnumSource
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	public void givenUatpAggregatedGeneralLedgerTransactionDetails_WhenProcessedAndVirtualServiceEnabled_VerifyMockConnectorCalled(DocType docType) {


		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
		aggregatedGeneralLedgerTransactionDetails.setGlobalId("UATP-3");
		aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);
		aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.SALE);

		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		uatpAggregatedTransactionProcessService.process(aggregatedTransaction, docType, "test header text");

		verify(vsSapConnector, times(1)).postToSap(any(GeneralLedgerEnvelope.class));

	}
}
