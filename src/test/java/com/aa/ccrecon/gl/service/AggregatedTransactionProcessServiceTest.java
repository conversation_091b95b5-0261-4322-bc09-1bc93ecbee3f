package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.config.WebClientConfig;
import com.aa.ccrecon.gl.entity.AccountingStatusEntity;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLSummaryEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.GlResponseEnum;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.request.PostJournal;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.repo.AccountingStatusEntityRepository;
import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLPostingRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLSummaryRepository;
import com.aa.ccrecon.gl.service.aggregategl.AggregatedTransactionProcessService;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.aa.ccrecon.gl.validation.ValidationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;

import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.REFUND_TEXT;
import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.SALE_TEXT;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getGlobalId;
import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.hasItem;
import static org.hamcrest.CoreMatchers.hasItems;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasProperty;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest
@ExtendWith(OutputCaptureExtension.class)
@EnableConfigurationProperties(value = SapRequestConfig.class)
@ActiveProfiles("test")
class AggregatedTransactionProcessServiceTest extends TestContainerSetup {

    @MockitoBean
    DirectSapConnector directSapConnector;

	@MockitoBean
	WebClient webClient;

	@MockitoBean
	WebClientConfig webClientConfig;

	@MockitoBean
	DirectSapConnector sapConnector;

	@Autowired
	AggregatedTransactionProcessService aggregatedTransactionProcessService;

	@Autowired
	AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo;

	@MockitoSpyBean
	ReceivableAccountingGLSummaryRepository receivableAccountingGLSummaryRepository;

	@Autowired
	ReceivableAccountingGLPostingRepository receivableAccountingGLPostingRepository;

	@Autowired
	AccountingStatusEntityRepository accountingStatusEntityRepository;

	@MockitoBean
	ValidationService validationService;

    private final ObjectMapper objectMapper = new ObjectMapper();

	/***
	 *
	 * @param capturedOutput
	 */
	@ParameterizedTest
	@EnumSource
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	void givenSAPGLRequest_WhenEntityIsProcessed_ThenVerifyGLEntityReturn(DocType docType, CapturedOutput capturedOutput) throws SapResponseException, IOException {
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
				null, "1", 0, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		ArgumentCaptor<GeneralLedgerEnvelope> reqCaptor = ArgumentCaptor.forClass(GeneralLedgerEnvelope.class);
		InputStreamReader r = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/sapresponse_success_single_post.json")));
		GlPostingResponse sapResp = objectMapper.readValue(r, GlPostingResponse.class);

		when(sapConnector.postToSap(reqCaptor.capture())).thenReturn(sapResp);

		aggregatedTransactionProcessService.process(aggregatedTransaction, docType, SALE_TEXT);

		List<AggregateReceivableAccountingTransEntity> entityList = aggregateReceivableAccountingTransRepo.findByGlobalId("1");
		List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findByGlobalId(getGlobalId.apply(aggregatedTransaction));
		List<ReceivableAccountingGLSummaryEntity> summary = receivableAccountingGLSummaryRepository.findAll();
		List<AccountingStatusEntity> accountingStatusEntities = accountingStatusEntityRepository.findAll();

		assertThat(entityList.getFirst().getGlobalId(), is("1"));

		assertThat(capturedOutput.getOut(), containsString("Number of records found: 2"));
		GeneralLedgerEnvelope req = reqCaptor.getValue();
		assertThat(postings.size(), is(1));
		assertThat(postings.getFirst().getTransactionType(), is("SALE"));

//		assertThat(summary.size(), is(1));
//		assertThat(summary.getFirst().getTransactionType(), is("SALE"));

		// Verify AccountingStatusEntity new fields
		assertFalse(accountingStatusEntities.isEmpty());
		AccountingStatusEntity accountingStatus = accountingStatusEntities.getFirst();
		assertEquals("123456", accountingStatus.getCardBin());
		assertEquals("4321", accountingStatus.getCardLastFourDigits());
		assertEquals("REF123", accountingStatus.getRefundId());
		assertEquals("SALE", accountingStatus.getTransactionType());

        PostJournal journal = req.getPostJournal();
		assertEquals(journal.getRoutingInfo().getSapDocType(), docType.name());
		assertEquals(SALE_TEXT, journal.getPostJournalDetail().getFirst().getHeaderTxt());
	}

	@Test
	@DisplayName("Given SAP GL request with multiple documents, when processed successfully, then verify GL entities are saved correctly")
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	void testMultipleSapDocuments_SavedCorrectly() throws SapResponseException, IOException {
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
				null, "1", 0, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		ArgumentCaptor<GeneralLedgerEnvelope> reqCaptor = ArgumentCaptor.forClass(GeneralLedgerEnvelope.class);
		InputStreamReader r = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/sap-resp-multiples.json")));
        GlPostingResponse sapResp = objectMapper.readValue(r, GlPostingResponse.class);

		when(sapConnector.postToSap(reqCaptor.capture())).thenReturn(sapResp);

		aggregatedTransactionProcessService.process(aggregatedTransaction, DocType.R9, REFUND_TEXT);

		List<AggregateReceivableAccountingTransEntity> entityList = aggregateReceivableAccountingTransRepo.findByGlobalId("1");
		List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findAll();
		List<ReceivableAccountingGLSummaryEntity> summary = receivableAccountingGLSummaryRepository.findAll();
		List<AccountingStatusEntity> accountingStatusEntities = accountingStatusEntityRepository.findAll();

		assertThat(entityList.size(), is(2));
		assertThat(entityList.getFirst().getGlobalId(), is("1"));

		assertThat(postings.size(), is(2));
		assertThat(postings, hasItems(
				hasProperty("sapDocNumber", is("**********")),
				hasProperty("sapDocNumber", is("**********"))
		));

//		assertThat(summary.size(), is(2));
//		assertThat(summary, hasItems(
//				hasProperty("sapDocNumber", is("**********")),
//				hasProperty("sapDocNumber", is("**********"))
//		));

		// Verify AccountingStatusEntity new fields
		assertFalse(accountingStatusEntities.isEmpty());
		assertThat(accountingStatusEntities, hasItem(hasProperty("transactionType", is("SALE"))));
		assertThat(accountingStatusEntities, hasItem(hasProperty("cardBin", is("123456"))));
		assertThat(accountingStatusEntities, hasItem(hasProperty("cardLastFourDigits", is("4321"))));
		assertThat(accountingStatusEntities, hasItem(hasProperty("refundId", is("REF123"))));

		GeneralLedgerEnvelope req = reqCaptor.getValue();

		PostJournal journalDoc = req.getPostJournal();
		assertEquals("R9", journalDoc.getRoutingInfo().getSapDocType());
		assertEquals(REFUND_TEXT, journalDoc.getPostJournalDetail().getFirst().getHeaderTxt());
	}


	@ParameterizedTest
    @Disabled
	@ValueSource(strings = {"1"})
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	void givenSAPGLRequest_WhenPIIDataMasked_ThenNotVisibleInLoGs(String globalId, CapturedOutput capturedOutput) {
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
				null, globalId, 0, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
		aggregatedTransactionProcessService.process(aggregatedTransaction, DocType.E9, SALE_TEXT);

		assertTrue(capturedOutput.getOut().contains("\"allocNmbr\": \"****\""));
		assertFalse(capturedOutput.getOut().matches("^assignmentNumber: [a-zA-Z0-9]*"));
	}


	@Test
	@Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
	public void givenSapTransactionRequest_WhenSapResponseErrors_VerifyGLPostingRepositoryEntries
			(CapturedOutput capturedOutput) throws SapResponseException {

		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
		aggregatedGeneralLedgerTransactionDetails.setGlobalId("1");
		aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_NON_UATP);
		aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.SALE);
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		SapResponseException exception = Mockito.mock(SapResponseException.class);
		when(exception.getBody()).thenReturn("Error");
		when(exception.getMessage()).thenReturn("Error Message");
		when(exception.getStatusCode()).thenReturn(500);

		when(sapConnector.postToSap(any(GeneralLedgerEnvelope.class)))
				.thenThrow(exception);

		aggregatedTransactionProcessService.process(aggregatedTransaction, DocType.E9, SALE_TEXT);

		List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findByGlobalId(aggregatedTransaction.getMessageEnvelope().getPayload().getAggregatedGeneralLedgerTransactionDetails().getGlobalId());
		List<AccountingStatusEntity> entities = accountingStatusEntityRepository.findAll();

		assertEquals(2, postings.size());
		assertTrue(postings.stream().allMatch(i -> i.getGlPostingStatus().equals(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString()) &&
				i.getTransactionType().equals(TransactionType.TYPES.SALE.toString())));
		assertTrue(capturedOutput.getOut().contains("Number of records found: 2"));
		assertEquals(10, entities.size());
		assertTrue(entities.stream().allMatch(i -> i.getStatus().equals(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString())));
		AccountingStatusEntity accountingStatus = entities.getFirst();
		assertEquals("123456", accountingStatus.getCardBin());
		assertEquals("4321", accountingStatus.getCardLastFourDigits());
		assertEquals("REF123", accountingStatus.getRefundId());
		assertEquals("SALE", accountingStatus.getTransactionType());

        verifyNoInteractions(receivableAccountingGLSummaryRepository);
    }

    @ValueSource(strings = { "sapresponse_duplicate.json", "sapresponse_error.json" })
    @ParameterizedTest
    @DisplayName("Given SAP request, when SAP returns an error, then verify GL posting repository entries are created with correct status")
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void sapErrorResponse_NotConnectionIssue(String fileName, CapturedOutput capturedOutput) throws SapResponseException, IOException {

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("1");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_NON_UATP);
        aggregatedGeneralLedgerTransactionDetails.setTransactionType(TransactionType.TYPES.SALE);
        AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

        InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/" + fileName)));
        GlPostingResponse response = objectMapper.readValue(isr, GlPostingResponse.class);

        when(sapConnector.postToSap(any(GeneralLedgerEnvelope.class)))
                .thenReturn(response);

        aggregatedTransactionProcessService.process(aggregatedTransaction, DocType.E9, SALE_TEXT);

        List<ReceivableAccountingGLEntity> postings = receivableAccountingGLPostingRepository.findByGlobalId(aggregatedTransaction.getMessageEnvelope().getPayload().getAggregatedGeneralLedgerTransactionDetails().getGlobalId());
        List<AccountingStatusEntity> entities = accountingStatusEntityRepository.findAll();

        assertEquals(2, postings.size());
        assertTrue(postings.stream()
                .allMatch(i -> i.getTransactionType().equals(TransactionType.TYPES.SALE.toString())));

        assertTrue(capturedOutput.getOut().contains("Number of records found: 2"));
        assertEquals(10, entities.size());
        if (fileName.equals("sapresponse_duplicate.json")) {
            assertTrue(postings.stream()
                    .allMatch(i -> i.getGlPostingStatus().equals(GlResponseEnum.GLPOSTING_DUPLICATE.toString())));
            assertTrue(entities.stream().allMatch(i -> i.getStatus().equals(GlResponseEnum.GLPOSTING_DUPLICATE.toString())));
        } else {
            assertTrue(postings.stream()
                    .allMatch(i -> i.getGlPostingStatus().equals(GlResponseEnum.GLPOSTING_ERROR.toString())));
            assertTrue(entities.stream().allMatch(i -> i.getStatus().equals(GlResponseEnum.GLPOSTING_ERROR.toString())));
        }
        AccountingStatusEntity accountingStatus = entities.getFirst();
        assertEquals("123456", accountingStatus.getCardBin());
        assertEquals("4321", accountingStatus.getCardLastFourDigits());
        assertEquals("REF123", accountingStatus.getRefundId());
        assertEquals("SALE", accountingStatus.getTransactionType());

        verifyNoInteractions(receivableAccountingGLSummaryRepository);
    }

}