package com.aa.ccrecon.gl.service;

import static org.mockito.Mockito.times;

import java.time.ZonedDateTime;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.service.aggregategl.LookupServiceImpl;

@ExtendWith(MockitoExtension.class)
public class LookupServiceImplTests {
	
	@Mock
	private AggregateReceivableAccountingTransRepo mockAggregateReceivableAccountingTransRepo;
	
	@InjectMocks
	private LookupServiceImpl lookupServiceImpl;
	
	@Test
	@DisplayName("Given globalId, when findAggregateReceivableAccountingTrans() is called with globalId, then repos findByGlobalIdOrderByCompanyCodeAscCurrencyAsc() is called with the globalId")
	public void testFindAggregateReceivableAccountingTrans() {
		int count = 5;
		String globalId = "1";
		AggregatedGeneralLedgerTransactionDetails sapTransReq = createAggregatedGeneralLedgerTransactionDetails(count, globalId);
		
		lookupServiceImpl.findAggregateReceivableAccountingTrans(sapTransReq);
		
		Mockito.verify(mockAggregateReceivableAccountingTransRepo, times(1)).findByGlobalIdOrderByCurrencyAsc(globalId);
		
	}
	
	private AggregatedGeneralLedgerTransactionDetails createAggregatedGeneralLedgerTransactionDetails(int count, String globaId) {
		
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
		aggregatedGeneralLedgerTransactionDetails.setCount(count);
		aggregatedGeneralLedgerTransactionDetails.setGlobalId(globaId);
		aggregatedGeneralLedgerTransactionDetails.setDateTimestamp(ZonedDateTime.now().toString());
		
		return aggregatedGeneralLedgerTransactionDetails;
	}

}
