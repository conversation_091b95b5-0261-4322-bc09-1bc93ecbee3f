package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.mapper.SapRequestMapper;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.service.aggregategl.AggregatedTransactionProcessService;
import com.aa.ccrecon.gl.service.aggregategl.LookupServiceImpl;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import com.aa.ccrecon.gl.validation.ValidationService;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.REFUND_TEXT;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AggregatedTransactionProcessServiceUnitTests {

	@Mock
	private LookupServiceImpl lookupService;
	@Mock
	private SapRequestMapper sapRequestMapper;
	@Mock
	private SapRequestConfig sapRequestConfig;
	@Mock
	private DirectSapConnector sapConnector;
	@Mock
	private ReturnStatusService returnStatusService;
	@Mock
	private ValidationService validationService;
	@InjectMocks
	private AggregatedTransactionProcessService aggregatedTransactionProcessService;

	@Test
	public void testProcess() throws SapResponseException {
		AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
		AggregatedTransaction aggregatedTransaction = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);

		AggregateReceivableAccountingTransEntity entity = new AggregateReceivableAccountingTransEntity();
		List<AggregateReceivableAccountingTransEntity> list = new ArrayList<>();
		list.add(entity);
		GeneralLedgerEnvelope sapRequest = createSapRequest();

		when(lookupService.findAggregateReceivableAccountingTrans(aggregatedGeneralLedgerTransactionDetails)).thenReturn(list);
		when(sapRequestMapper.mapGlEnvelope(any(), any(), any())).thenReturn(sapRequest);
		when(sapConnector.postToSap(sapRequest)).thenThrow(new SapResponseException("body", "message", 500));

		aggregatedTransactionProcessService.process(aggregatedTransaction, DocType.R9, REFUND_TEXT);

		verify(returnStatusService, times(1))
                .storeErrorAndRetry(any(AggregatedTransaction.class), any(AggregatedGeneralLedgerTransactionDetails.class),
                        any(GeneralLedgerEnvelope.class), any(SapResponseException.class), any(MessageType.class));
        verify(sapRequestMapper, times(1)).mapGlEnvelope(any(), any(), any());
	}

	private GeneralLedgerEnvelope createSapRequest() {
		String sapRequestString = """
                {
                   "PostJournal": {
                     "Header": {
                       "EXEC_MODE": "S",
                       "PROC_OPTION": "P",
                       "IDENTIFIER": "",
                       "SPLIT_LINE": "",
                       "REVERSAL": "",
                       "REVERSAL_DATE": "",
                       "Rev_reason": ""
                     },
                     "Routing_Info": {
                       "Domain": "ARA-AirRevAcctg",
                       "Interface": "PI-I-035",
                       "Source": "ARA",
                       "Destination": "SAP",
                       "Service": "GL Service",
                       "TransType": "BAPI",
                       "SapDocType": "ARA",
                       "User_ID": "",
                       "Date_Updated": "********",
                       "Time_Updated": "174008",
                       "Global_ID": "ARA_GL_********_174008_281_debugjp2"
                     },
                     "PostJournal_Detail": [
                       {
                         "DOC_TYPE": "Q1",
                         "COMP_CODE": "AA00",
                         "CURRENCY": "USD",
                         "PMTMTHSUPL": "",
                         "PSTNG_DATE": "********",
                         "FIS_PERIOD": "08",
                         "HEADER_TXT": "********1534_SA",
                         "NEWCOMP": "AABB",
                         "REF_DOC_NO": "********1534_SA",
                         "DOC_DATE": "********",
                         "POST_KEY": "50",
                         "ACCOUNT_NO": "2040200",
                         "CUSTOMER": "",
                         "VENDOR": "",
                         "AMT_DOC_CURR": "69.00",
                         "TAX_CODE": "",
                         "PMNTTRMS": "",
                         "PYMT_METH": "",
                         "COSTCENTER": "",
                         "PROFIT_CTR": "",
                         "ALLOC_NMBR": "DI",
                         "ITEM_TEXT": "",
                         "ORDERID": "",
                         "NETWORK": "",
                         "PO_NUMBER": "",
                         "PO_ITEM": "",
                         "QUANTITY": "",
                         "BASE_UOM": "",
                         "VALUE_DATE": "",
                         "ZASSET": "",
                         "PERSONNEL_NO": "",
                         "BUS_AREA": "",
                         "TAX_AMT": "",
                         "TRADE_ID": "",
                         "AMT_LOCALCUR": "",
                         "AMT_SECONDLOCALCUR": "",
                         "ASSET_TRANSTYPE": "",
                         "EXCH_RATE": "",
                         "DATS": "",
                         "ASVAL_DATE": "",
                         "TAXJURCODE": "",
                         "MATERIAL": "",
                         "BUSINESSPLACE": "",
                         "INVOICE_NO": "",
                         "INVOICE_FISCAL_YEAR": "",
                         "INVOICE_LINEITEM": "",
                         "REF_KEY_1": "",
                         "REF_KEY_2": "",
                         "REF_KEY_3": "",
                         "CURR_LOCAL": "",
                         "CURR_GROUP": "USD",
                         "LONG_TEXT": "7cf7a717-5603-4ff9-86cb-d3c90b9ad297_debugjp2"
                       }
                     ]
                   }
                 }""";
		return new Gson().fromJson(sapRequestString, GeneralLedgerEnvelope.class);
	}
}
