package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;

class TransactionTypeMappingServiceTest {

    private TransactionTypeMappingService transactionTypeMappingService;

    @BeforeEach
    void setUp() {
        transactionTypeMappingService = new TransactionTypeMappingService();
    }

    @Test
    void givenSaleTransactionType_WhenGetMapping_ThenReturnCreditWithPostKey50() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.SALE);

        assertThat(mapping.getDebitCredit(), is(AccountType.CREDIT));
        assertThat(mapping.getPostKey(), is("50"));
    }

    @Test
    void givenRefundTransactionType_WhenGetMapping_ThenReturnDebitWithPostKey40() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.REFUND);

        assertThat(mapping.getDebitCredit(), is(AccountType.DEBIT));
        assertThat(mapping.getPostKey(), is("40"));
    }

    @Test
    void givenChargebackTransactionType_WhenGetMapping_ThenReturnDebitWithPostKey40() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.CHARGEBACK);

        assertThat(mapping.getDebitCredit(), is(AccountType.DEBIT));
        assertThat(mapping.getPostKey(), is("40"));
    }

    @Test
    void givenFeeTransactionType_WhenGetMapping_ThenReturnDebitWithPostKey40() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.FEE);

        assertThat(mapping.getDebitCredit(), is(AccountType.DEBIT));
        assertThat(mapping.getPostKey(), is("40"));
    }

    @Test
    void givenBankwireTransactionType_WhenGetMapping_ThenReturnDebitWithPostKey40() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.BANKWIRE);

        assertThat(mapping.getDebitCredit(), is(AccountType.DEBIT));
        assertThat(mapping.getPostKey(), is("40"));
    }

}
