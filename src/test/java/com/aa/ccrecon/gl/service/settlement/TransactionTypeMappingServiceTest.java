package com.aa.ccrecon.gl.service.settlement;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;

class TransactionTypeMappingServiceTest {

    private TransactionTypeMappingService transactionTypeMappingService;
    private ListAppender<ILoggingEvent> listAppender;
    private Logger logger;

    @BeforeEach
    void setUp() {
        transactionTypeMappingService = new TransactionTypeMappingService();
        
        // Set up log capture
        logger = (Logger) LoggerFactory.getLogger(TransactionTypeMappingService.class);
        listAppender = new ListAppender<>();
        listAppender.start();
        logger.addAppender(listAppender);
        logger.setLevel(Level.INFO);
    }

    @Test
    void givenSaleTransactionType_WhenGetMapping_ThenReturnCreditWithPostKey50() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.TYPES.SALE);
        
        assertThat(mapping.getDebitCredit(), is(AccountType.CREDIT));
        assertThat(mapping.getPostKey(), is("50"));
    }

    @Test
    void givenRefundTransactionType_WhenGetMapping_ThenReturnDebitWithPostKey40() {
        TransactionTypeMapping mapping = transactionTypeMappingService.getTransactionTypeMapping(TransactionType.TYPES.REFUND);
        
        assertThat(mapping.getDebitCredit(), is(AccountType.DEBIT));
        assertThat(mapping.getPostKey(), is("40"));
    }

    @Test
    void givenSaleTransaction_WhenLogMapping_ThenLogCorrectMapping() {
        AggregatedGeneralLedgerTransactionDetails details = new AggregatedGeneralLedgerTransactionDetails(
                "", "test-global-id", 1, MessageType.AGGREGATED_GL_NON_UATP, 
                SalesSource.COMPANY, TransactionType.TYPES.SALE, 
                AccountingType.SETTLEMENT_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction transaction = TestModelExtractor.createAggregateTransaction.apply(details);

        transactionTypeMappingService.logTransactionTypeMapping(transaction);

        assertThat(listAppender.list.size(), is(1));
        ILoggingEvent logEvent = listAppender.list.get(0);
        assertThat(logEvent.getLevel(), is(Level.INFO));
        assertThat(logEvent.getFormattedMessage(), containsString("Transaction Type Mapping"));
        assertThat(logEvent.getFormattedMessage(), containsString("Global ID: test-global-id"));
        assertThat(logEvent.getFormattedMessage(), containsString("Transaction Type: SALE"));
        assertThat(logEvent.getFormattedMessage(), containsString("Debit/Credit: CREDIT"));
        assertThat(logEvent.getFormattedMessage(), containsString("Post Key: 50"));
    }

    @Test
    void givenRefundTransaction_WhenLogMapping_ThenLogCorrectMapping() {
        AggregatedGeneralLedgerTransactionDetails details = new AggregatedGeneralLedgerTransactionDetails(
                "", "test-global-id-refund", 1, MessageType.AGGREGATED_GL_NON_UATP, 
                SalesSource.COMPANY, TransactionType.TYPES.REFUND, 
                AccountingType.SETTLEMENT_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction transaction = TestModelExtractor.createAggregateTransaction.apply(details);

        transactionTypeMappingService.logTransactionTypeMapping(transaction);

        assertThat(listAppender.list.size(), is(1));
        ILoggingEvent logEvent = listAppender.list.get(0);
        assertThat(logEvent.getLevel(), is(Level.INFO));
        assertThat(logEvent.getFormattedMessage(), containsString("Transaction Type Mapping"));
        assertThat(logEvent.getFormattedMessage(), containsString("Global ID: test-global-id-refund"));
        assertThat(logEvent.getFormattedMessage(), containsString("Transaction Type: REFUND"));
        assertThat(logEvent.getFormattedMessage(), containsString("Debit/Credit: DEBIT"));
        assertThat(logEvent.getFormattedMessage(), containsString("Post Key: 40"));
    }

    @Test
    void givenTransactionWithNullTransactionType_WhenLogMapping_ThenLogWarning() {
        AggregatedGeneralLedgerTransactionDetails details = new AggregatedGeneralLedgerTransactionDetails(
                "", "test-global-id-null", 1, MessageType.AGGREGATED_GL_NON_UATP, 
                SalesSource.COMPANY, null, 
                AccountingType.SETTLEMENT_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction transaction = TestModelExtractor.createAggregateTransaction.apply(details);

        transactionTypeMappingService.logTransactionTypeMapping(transaction);

        assertThat(listAppender.list.size(), is(1));
        ILoggingEvent logEvent = listAppender.list.get(0);
        assertThat(logEvent.getLevel(), is(Level.WARN));
        assertThat(logEvent.getFormattedMessage(), containsString("Transaction type is null for Global ID: test-global-id-null"));
    }
}
