package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.model.AccountType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransactionTypeMappingServiceTest {

    @Mock
    private SapRequestConfig sapRequestConfig;
    
    private TransactionTypeMappingService transactionTypeMappingService;

    @BeforeEach
    void setUp() {
        transactionTypeMappingService = new TransactionTypeMappingService(sapRequestConfig);
        
        // Mock the configuration values
        when(sapRequestConfig.getPostingKeyCredit()).thenReturn("50");
        when(sapRequestConfig.getPostingKeyDebit()).thenReturn("40");
    }

    @Test
    void givenSaleTransactionType_WhenGetAccountType_ThenReturnCredit() {
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.SALE);
        
        assertThat(accountType, is(AccountType.CREDIT));
    }

    @Test
    void givenRefundTransactionType_WhenGetAccountType_ThenReturnDebit() {
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.REFUND);
        
        assertThat(accountType, is(AccountType.DEBIT));
    }

    @Test
    void givenChargebackTransactionType_WhenGetAccountType_ThenReturnDebit() {
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.CHARGEBACK);
        
        assertThat(accountType, is(AccountType.DEBIT));
    }

    @Test
    void givenFeeTransactionType_WhenGetAccountType_ThenReturnDebit() {
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.FEE);
        
        assertThat(accountType, is(AccountType.DEBIT));
    }

    @Test
    void givenBankwireTransactionType_WhenGetAccountType_ThenReturnDebit() {
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.BANKWIRE);
        
        assertThat(accountType, is(AccountType.DEBIT));
    }

    @Test
    void givenCreditAccountType_WhenGetPostKey_ThenReturn50() {
        String postKey = transactionTypeMappingService.getPostKey(AccountType.CREDIT);
        
        assertThat(postKey, is("50"));
    }

    @Test
    void givenDebitAccountType_WhenGetPostKey_ThenReturn40() {
        String postKey = transactionTypeMappingService.getPostKey(AccountType.DEBIT);
        
        assertThat(postKey, is("40"));
    }

    @Test
    void givenSaleTransactionType_WhenGetFullMapping_ThenReturnCreditAndPostKey50() {
        // Test the full flow
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.SALE);
        String postKey = transactionTypeMappingService.getPostKey(accountType);
        
        assertThat(accountType, is(AccountType.CREDIT));
        assertThat(postKey, is("50"));
    }

    @Test
    void givenRefundTransactionType_WhenGetFullMapping_ThenReturnDebitAndPostKey40() {
        // Test the full flow
        AccountType accountType = transactionTypeMappingService.getAccountType(TransactionType.REFUND);
        String postKey = transactionTypeMappingService.getPostKey(accountType);
        
        assertThat(accountType, is(AccountType.DEBIT));
        assertThat(postKey, is("40"));
    }
}
