package com.aa.ccrecon.gl.message;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.config.AppConfig;
import com.aa.ccrecon.gl.service.doctype.DocTypeLocator;
import com.aa.ccrecon.gl.util.TestModelExtractor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MessageQueueProcessorTest {
    @Mock
    private MessageQueuePublisher messageQueuePublisher;
    @Mock
    private AppConfig appConfig;
    @InjectMocks
    private MessageQueueProcessor processor;
    @Mock
    private DocTypeLocator docTypeService;

    @BeforeEach
    void setUp() {
        when(appConfig.getMessageRetryCountLimit()).thenReturn(1);
    }

    @DisplayName("Given a message, when retry count is less than limit, then send message to accounting GL queue")
    @Test
    void testRetryTransactionRequestLessThanLimit() {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction trans = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
        trans.getMessageEnvelope().getPayload().getCcReconHeader().setMessageEventRescheduleCount(0);

        processor.retryTransactionRequest(trans);

        verify(messageQueuePublisher, times(1)).sendToAccountingGlQueue(any(AggregatedTransaction.class));
        verify(messageQueuePublisher, times(0)).sendToExceptionQueue(any(AggregatedTransaction.class));
    }

    @DisplayName("Given a message, when retry count is over the limit, then send message to error GL queue")
    @Test
    void testRetryTransactionRequestOverLimit() {
        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails(
                "", "1", 1, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, TransactionType.TYPES.SALE, AccountingType.RECEIVABLE_ACCOUNTING, "AFFIRM", NotificationType.MANUAL);
        AggregatedTransaction trans = TestModelExtractor.createAggregateTransaction.apply(aggregatedGeneralLedgerTransactionDetails);
        trans.getMessageEnvelope().getPayload().getCcReconHeader().setMessageEventRescheduleCount(1);

        processor.retryTransactionRequest(trans);

        verify(messageQueuePublisher, times(0)).sendToAccountingGlQueue(any(AggregatedTransaction.class));
        verify(messageQueuePublisher, times(1)).sendToExceptionQueue(any(AggregatedTransaction.class));
    }
}