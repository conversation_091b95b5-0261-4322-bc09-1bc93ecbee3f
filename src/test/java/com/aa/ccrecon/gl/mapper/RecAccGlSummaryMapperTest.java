package com.aa.ccrecon.gl.mapper;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.model.sap.response.PostingDetails;
import org.junit.jupiter.api.Test;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

class RecAccGlSummaryMapperTest {

    private RecAccGlSummaryMapper mapper = new RecAccGlSummaryMapperImpl();

    @Test
    void map() {
        PostingDetails pd = new PostingDetails();
        pd.setCompanyCode("AA00");
        pd.setLocalCurrencyCode("USD");
        pd.setPostingAmountLocalCurrency("100.00");
        pd.setPostingAmountUsd("100.00");
        pd.setSapOperationalDocumentId("doc-id");
        pd.setPostingYear("2025");
        AggregatedGeneralLedgerTransactionDetails aggDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggDetails.setTransactionType(TransactionType.TYPES.SALE);
        aggDetails.setGlobalId("1");

        var result = mapper.map(pd, aggDetails);

        assertThat(result.getTransactionType(), is(TransactionType.TYPES.SALE.name()));
        assertThat(result.getGlobalId(), is("1"));
        assertThat(result.getCompanyCode(), is("AA00"));
        assertThat(result.getGlPostYear(), is(2025));
        assertThat(result.getLocalCurrency(), is("USD"));
        assertThat(result.getResponseSubTotal().toString(), is("100.00"));
        assertThat(result.getUsdConvertedSubTotal().toString(), is("100.00"));
        assertThat(result.getSapDocNumber(), is("doc-id"));
    }
}