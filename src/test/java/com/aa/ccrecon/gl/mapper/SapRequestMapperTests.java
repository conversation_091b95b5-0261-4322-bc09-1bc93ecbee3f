package com.aa.ccrecon.gl.mapper;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.gl.client.DirectSapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.config.WebClientConfig;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.request.Header;
import com.aa.ccrecon.gl.model.sap.request.JournalEntryItem;
import com.aa.ccrecon.gl.model.sap.request.RoutingInformation;
import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.repo.UatpAggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.util.TestContainerSetup;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static com.aa.ccrecon.gl.utils.ModelExtractor.getJournalEntryDetails;
import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.emptyOrNullString;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;


@SpringBootTest
@EnableConfigurationProperties(value = SapRequestConfig.class)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
public class SapRequestMapperTests extends TestContainerSetup {

    @Autowired
    private SapRequestMapper sapRequestMapper;

    @Autowired
    private AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo;

    @Autowired
    private UatpAggregateReceivableAccountingTransRepo uatpAggregateReceivableAccountingTransRepo;

    @Autowired
    private SapRequestConfig sapRequestConfig;

    @MockitoBean
    WebClientConfig webClientConfig;

    @MockitoBean
    DirectSapConnector sapConnectorImpl;

    @MockitoBean
    DirectSapConnector directSapConnector;


    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpData_whenMappedToGlEnvelope_thenValidateMapping() {
        List<UatpAggregateReceivableAccountingTransEntity> entities = uatpAggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc("UATP-3");

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("UATP-3");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);

        GeneralLedgerEnvelope glEnv = sapRequestMapper.mapGlEnvelopeUatp(entities, aggregatedGeneralLedgerTransactionDetails, sapRequestConfig);
        validateUatpMapping(glEnv);
    }


    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenUatpData_whenMappedToGlEnvelope_thenValidateRefundMapping() {
        List<UatpAggregateReceivableAccountingTransEntity> entities = uatpAggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc("UATP-3");

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("UATP-3");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_UATP);

        sapRequestConfig.setDocType(DocType.R9.name());
        GeneralLedgerEnvelope glEnv = sapRequestMapper.mapGlEnvelopeUatp(entities, aggregatedGeneralLedgerTransactionDetails, sapRequestConfig);
        validateMappingUatpRefund(glEnv);
    }

    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenNonUatpData_whenMappedToSapRequest_thenValidateMapping_GlEnvelope() {
        List<AggregateReceivableAccountingTransEntity> entities = aggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc("1");

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("1");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_NON_UATP);

        sapRequestConfig.setDocType(DocType.E9.name());
        GeneralLedgerEnvelope glEnvelope = sapRequestMapper.mapGlEnvelope(entities, aggregatedGeneralLedgerTransactionDetails, sapRequestConfig);
        validateHeader(glEnvelope.getPostJournal().getHeader());
        validateRoutingInfo(glEnvelope.getPostJournal().getRoutingInfo(), aggregatedGeneralLedgerTransactionDetails);
        validateJournalEntryItemMapping(glEnvelope);
        getJournalEntryDetails.apply(glEnvelope)
                .ifPresent(list -> list.forEach(
                        jeid -> {
                            if (jeid.getPostKey().equals("40")) {
                                // in test data, debits have posting key 40 and company code AA00
                                // converted_subtotal_amount
                                assertEquals("*************.16", jeid.getAmtLocalCur());
                                // converted_currency
                                assertEquals("BRL", jeid.getCurrLocal());
                                assertThat(jeid.getAccountNo(), not(emptyOrNullString()));
                            } else {
                                // in test data, debits have posting key 50 and company code != AA00
                                // sub_total
                                assertEquals("*************.69", jeid.getAmtLocalCur());
                                // currency
                                assertEquals("EUR", jeid.getCurrLocal());
                                assertThat(jeid.getAccountNo(), not(emptyOrNullString()));
                            }
                            // these values should be same for all items
                            assertEquals("*************.16", jeid.getAmtSecondLocalCur());
                            assertEquals("BRL", jeid.getCurrGroup());
                        }));

    }


    @Test
    @Sql(value = "/sql/generator.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    public void givenNonUatpData_whenMappedToGlEnvelopeForRefund_thenValidateMapping() {
        List<AggregateReceivableAccountingTransEntity> entities = aggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc("1");

        AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggregatedGeneralLedgerTransactionDetails.setGlobalId("1");
        aggregatedGeneralLedgerTransactionDetails.setMessageType(MessageType.AGGREGATED_GL_NON_UATP);

        sapRequestConfig.setDocType(DocType.R9.name());
        GeneralLedgerEnvelope glEnvelope = sapRequestMapper.mapGlEnvelope(entities, aggregatedGeneralLedgerTransactionDetails, sapRequestConfig);
        validateMappingRefund(glEnvelope);
    }

    private void validateUatpMapping(GeneralLedgerEnvelope glEnvelope) {
        List<JournalEntryItem> journalEntryItems = getJournalEntryDetails.apply(glEnvelope)
                .orElseGet(() -> fail("Journal Entry Item Data is empty"));
        if (!journalEntryItems.isEmpty()) {
            journalEntryItems.forEach(data -> {
                assertTrue(StringUtils.isNotEmpty(data.getCompCode()));
                assertEquals(data.getDocDate(), data.getValueDate());
                if (data.getPostKey().equals("40")) {
                    if (data.getCompCode().equals("AA00")) {
                        assertEquals("*************.16", data.getAmtLocalCur());
                        assertEquals("BRL", data.getCurrLocal());
                    } else {
                        assertEquals("*************.69", data.getAmtLocalCur());
                        assertEquals("EUR", data.getCurrLocal());
                    }
                } else {
                    if (data.getCompCode().equals("AA00")) {
                        assertEquals("*************.16", data.getAmtLocalCur());
                        assertEquals("BRL", data.getCurrLocal());
                    } else {
                        assertEquals("*************.69", data.getAmtLocalCur());
                        assertEquals("EUR", data.getCurrLocal());
                    }
                }
            });
        } else {
            fail("Journal Entry Item Data is empty");
        }
    }

    private void validateMappingRefund(GeneralLedgerEnvelope glEnvelope) {
        List<JournalEntryItem> jei = getJournalEntryDetails.apply(glEnvelope)
                .orElseGet(() -> fail("Journal Entry Item Data is empty"));
        if (!jei.isEmpty()) {
            jei.forEach(data -> {
                assertTrue(StringUtils.isNotEmpty(data.getCompCode()));
                assertEquals(data.getDocDate(), data.getValueDate());
                if (data.getPostKey().equals("40")) {
                    assertNull(data.getAllocNmbr());
                } else if (data.getPostKey().equals("50")) {
                    assertNotNull(data.getAllocNmbr());
                    assertEquals("*************.69", data.getAmtLocalCur());
                    // currency
                    assertEquals("EUR", data.getCurrLocal());
                }
            });
        } else {
            fail("Journal Entry Item Data is empty");
        }
    }

    private void validateMappingUatpRefund(GeneralLedgerEnvelope glEnvelope) {
        List<JournalEntryItem> jei = getJournalEntryDetails.apply(glEnvelope)
                .orElseGet(() -> fail("Journal Entry Item Data is empty"));
        if (!jei.isEmpty()) {
            jei.forEach(data -> {
                assertTrue(StringUtils.isNotEmpty(data.getCompCode()));
                assertEquals(data.getDocDate(), data.getValueDate());
                if (data.getPostKey().equals("40")) {
                    assertNull(data.getAllocNmbr());
                } else if (data.getPostKey().equals("50")) {
                    assertNotNull(data.getAllocNmbr());
                    assertEquals("*************.69", data.getAmtLocalCur());
                    // currency
                    assertEquals("EUR", data.getCurrLocal());
                }
            });
        } else {
            fail("Journal Entry Item Data is empty");
        }
    }

    private void validateJournalEntryItemMapping(GeneralLedgerEnvelope glEnv) {
        List<JournalEntryItem> journalEntryItemData = getJournalEntryDetails.apply(glEnv)
                .orElseGet(() -> fail("Journal Entry Item Data is empty"));
        if (!journalEntryItemData.isEmpty()) {
            journalEntryItemData.forEach(data -> {
                assertTrue(StringUtils.isNotEmpty(data.getCompCode()));
                assertEquals(data.getDocDate(), data.getValueDate());
                assertThat(data.getPostingDate(), not(emptyOrNullString()));
                assertThat(data.getFiscalPeriod(), not(emptyOrNullString()));
                assertThat(data.getHeaderTxt(), is(sapRequestConfig.getDocumentHeaderText()));
            });
        } else {
            fail("Journal Entry Item Data is empty");
        }
    }

    private void validateHeader(Header header) {
        assertNotNull(header);
        assertThat(header.getExecMode(), is(sapRequestConfig.getExecMode()));
        assertThat(header.getProcOption(), is(sapRequestConfig.getProcOption()));
    }

    private void validateRoutingInfo(RoutingInformation routingInfo, AggregatedGeneralLedgerTransactionDetails dto) {
        assertNotNull(routingInfo);
        assertThat(routingInfo.getDomain(), is(sapRequestConfig.getDomain()));
        assertThat(routingInfo.getInterfaceName(), is(sapRequestConfig.getInterFace()));
        assertThat(routingInfo.getSource(), is(sapRequestConfig.getSource()));
        assertThat(routingInfo.getDestination(), is(sapRequestConfig.getDestination()));
        assertThat(routingInfo.getService(), is(sapRequestConfig.getService()));
        assertThat(routingInfo.getTransType(), is(sapRequestConfig.getTranstype()));
        assertThat(routingInfo.getSapDocType(), is(sapRequestConfig.getDocType()));
        assertThat(routingInfo.getUserId(), is(sapRequestConfig.getUserId()));
        assertThat(routingInfo.getGlobalId(), is(dto.getGlobalId()));
        assertThat(routingInfo.getDateUpdated(), not(emptyOrNullString()));
        assertThat(routingInfo.getTimeUpdated(), not(emptyOrNullString()));
    }

}
