package com.aa.ccrecon.gl.mapper;


import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.exception.SapResponseExceptionDto;
import com.aa.ccrecon.gl.model.GlResponseEnum;
import com.aa.ccrecon.gl.model.request.SapRequest;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Objects;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

public class RecAccGLPostingMapperTests {
    private final ObjectMapper objectMapper = new ObjectMapper();

    private RecAccGLPostingMapper mapper;

    @BeforeEach
    void setup() {
        mapper = Mappers.getMapper(RecAccGLPostingMapper.class);
    }

    @Test
    @DisplayName("Given an SAP response and transaction details, when mapping to the ReceivableAccountingGLEntity, " +
            "then the fields should be correctly mapped.")
    public void testMapGlEnvelopeResponse() throws IOException {
        GeneralLedgerEnvelope glEnvelope = createGeneralLedgerEnvelope();
        GlPostingResponse response = createGlPostingResponse();
        AggregatedGeneralLedgerTransactionDetails aggDetails = createAggGLTransDetails();

        var postingDetails = response.getPostingDetails().getFirst();

        ReceivableAccountingGLEntity result = mapper.mapSuccessResponse(response, glEnvelope, postingDetails, aggDetails);

        assertThat(result.getSapDocNumber(), is(postingDetails.getSapOperationalDocumentId()));
        assertThat(result.getCompanyCode(), is(postingDetails.getCompanyCode()));
        assertThat(result.getGlPostYear(), is(Integer.parseInt(postingDetails.getPostingYear())));
        assertThat(result.getGlPostingStatus(), is(GlResponseEnum.GLPOSTING_SUCCESS.name()));
        assertThat(result.getRequest(), is(new Gson().toJson(glEnvelope)));
        assertThat(result.getResponse(), is(new Gson().toJson(response)));
        assertThat(result.getTransactionType(), is(aggDetails.getTransactionType().name()));
        assertThat(result.getGlobalId(), is(aggDetails.getGlobalId()));
        assertThat(result.getLocalCurrency(), is(postingDetails.getLocalCurrencyCode()));
    }

    private AggregatedGeneralLedgerTransactionDetails createAggGLTransDetails() {
        AggregatedGeneralLedgerTransactionDetails aggDetails = new AggregatedGeneralLedgerTransactionDetails();
        aggDetails.setGlobalId("global-1");
        aggDetails.setTransactionType(TransactionType.TYPES.SALE);
        return aggDetails;
    }

    private SapRequest createSapRequest() {
		String sapRequestString = """
                {
                    "PostGeneralLedgerRqEnvelope": {
                        "Body": {
                            "postGeneralLedgerRq": {
                                "journalDocument": {
                                    "routingInfo": {
                                        "domain": "AR-C2",
                                        "interface": "PI-I-035",
                                        "source": "C2",
                                        "destination": "SAP",
                                        "service": "GL Service",
                                        "transType": "BAPI",
                                        "sapDocType": "E9",
                                        "userId": "S1013196",
                                        "dateUpdated": "********",
                                        "timeUpdated": "163601",
                                        "globalUniqueId": "****************"
                                    },
                                    "header": {
                                        "execMode": "S",
                                        "procOption": "P"
                                    },
                                    "journalEntryItems": [
                                        {
                                            "journalEntryItem": {
                                                "sequenceNo": "1",
                                                "docType": "E9",
                                                "companyCode": "AA00",
                                                "currency": "USD",
                                                "postingDate": "********",
                                                "fiscalPeriod": "02",
                                                "documentHeaderText": "GATEWAY RECEIVABLE",
                                                "referenceDocumentnumber": "1234",
                                                "ref_doc_no": "qqww",
                                                "documentDate": "********",
                                                "postingKey": "40",
                                                "accountNumber": "1240132",
                                                "amountInDocumentCurrency": "*************.72",
                                                "assignmentNumber": "B20240214",
                                                "itemText": "Affirm Co Sales Billing"
                                            }
                                        },
                                        {
                                            "journalEntryItem": {
                                                "sequenceNo": "2",
                                                "docType": "E9",
                                                "companyCode": "AA00",
                                                "currency": "USD",
                                                "postingDate": "********",
                                                "fiscalPeriod": "02",
                                                "documentHeaderText": "GATEWAY RECEIVABLE",
                                                "referenceDocumentnumber": "123",
                                                "documentDate": "********",
                                                "postingKey": "50",
                                                "accountNumber": "1240071",
                                                "amountInDocumentCurrency": "*************.72",
                                                "itemText": "Affirm Co Sales Billing",
                                                "ref_doc_no": "qqww"
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                }""";
		Gson g = new Gson();
        return g.fromJson(sapRequestString, SapRequest.class);
	}

    private GeneralLedgerEnvelope createGeneralLedgerEnvelope() throws JsonProcessingException {
        String sapRequestString = """
                {
                     "PostJournal": {
                         "Header": {
                             "EXEC_MODE": "S",
                             "PROC_OPTION": "P",
                             "IDENTIFIER": null,
                             "SPLIT_LINE": null,
                             "REVERSAL": null,
                             "REVERSAL_DATE": null,
                             "Rev_reason": null
                         },
                         "Routing_Info": {
                             "Domain": "PI-I-035",
                             "Interface": "PI-I-035",
                             "Source": "C2",
                             "Destination": "SAP",
                             "Service": "GL Service",
                             "TransType": "BAPI",
                             "SapDocType": "R9",
                             "User_ID": "S1013196",
                             "Date_Updated": "********",
                             "Time_Updated": "173432",
                             "Global_ID": "T********_4"
                         },
                         "PostJournal_Detail": [
                             {
                                 "DOC_TYPE": "R9",
                                 "COMP_CODE": "AA00",
                                 "CURRENCY": "USD",
                                 "PMTMTHSUPL": null,
                                 "PSTNG_DATE": "********",
                                 "FIS_PERIOD": "08",
                                 "HEADER_TXT": "C2 Refunds",
                                 "NEWCOMP": null,
                                 "REF_DOC_NO": "202506161810_SA",
                                 "DOC_DATE": "********",
                                 "POST_KEY": "50",
                                 "ACCOUNT_NO": "1240152",
                                 "CUSTOMER": null,
                                 "VENDOR": null,
                                 "AMT_DOC_CURR": "*************.29",
                                 "TAX_CODE": null,
                                 "PMNTTRMS": null,
                                 "PYMT_METH": null,
                                 "COSTCENTER": null,
                                 "PROFIT_CTR": null,
                                 "ALLOC_NMBR": "*************",
                                 "ITEM_TEXT": "C2 Refund Billing",
                                 "ORDERID": null,
                                 "NETWORK": null,
                                 "PO_NUMBER": null,
                                 "PO_ITEM": null,
                                 "QUANTITY": null,
                                 "BASE_UOM": null,
                                 "VALUE_DATE": "********",
                                 "ZASSET": null,
                                 "Asset_SUB_NUMBER": null,
                                 "PERSONNEL_NO": null,
                                 "BUS_AREA": null,
                                 "TAX_AMT": null,
                                 "TRADE_ID": null,
                                 "AMT_LOCALCUR": "0000000000931.40",
                                 "AMT_SECONDLOCALCUR": "0000000000931.40",
                                 "ASSET_TRANSTYPE": null,
                                 "EXCH_RATE": null,
                                 "DATS": null,
                                 "ASVAL_DATE": null,
                                 "TAXJURCODE": null,
                                 "MATERIAL": null,
                                 "BUSINESSPLACE": null,
                                 "INVOICE_NO": null,
                                 "INVOICE_FISCAL_YEAR": null,
                                 "INVOICE_LINEITEM": null,
                                 "REF_KEY_1": null,
                                 "REF_KEY_2": null,
                                 "REF_KEY_3": null,
                                 "CURR_LOCAL": "USD",
                                 "CURR_GROUP": "USD",
                                 "LONG_TEXT": null
                             }
                         ]
                     }
                 }""";
        return objectMapper.readValue(sapRequestString, GeneralLedgerEnvelope.class);
    }

	private AggregateReceivableAccountingTransEntity createAggRecAccTransEntity() {
		AggregateReceivableAccountingTransEntity entity = new AggregateReceivableAccountingTransEntity();
		entity.setReconId("RECON_ID");
		entity.setAggregateId("1l");
		entity.setGlobalId("1");
		entity.setBatchNumber(1);

		return entity;
	}

	private SapResponseExceptionDto createSapResponseExceptionDto() {
		String body = """
                {
                    "statusCode": "503",
                    "Description": "Destination Unavailable: Backend Service Not Available"
                }""";
		SapResponseExceptionDto response = new SapResponseExceptionDto();
		response.setBody(body);
		response.setMessage("message");
		response.setStatusCode(503);
		return response;
	}

    private GlPostingResponse createGlPostingResponse() throws IOException {
        InputStreamReader r = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/json/sapresponse_success_single_post.json")));
        return objectMapper.readValue(r, GlPostingResponse.class);
    }
}