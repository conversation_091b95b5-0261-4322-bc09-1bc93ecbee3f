{"PostGeneralLedgerRqEnvelope": {"Body": {"postGeneralLedgerRq": {"journalDocument": {"routingInfo": {"domain": "AR-MBS", "interface": "PI-I-035", "source": "MBS", "destination": "SAP", "service": "MBS_001", "transType": "BAPI", "sapDocType": "MBS", "userId": "MBSBATCH", "dateUpdated": "********", "timeUpdated": "130527", "globalUniqueId": "INV1234569 ******** 130527"}, "header": {"execMode": "S", "procOption": "P", "identifier": "", "splitLine": "", "reversal": "", "reversalDate": "", "reversalReason": ""}, "journalEntryItems": [{"journalEntryItem": {"@sequenceNo": "1", "docType": "MB", "companyCode": "AAJP", "currency": "JPY", "paymentMethodSupplement": "TU", "postingDate": "********", "fiscalPeriod": "02", "documentHeaderText": "2nd DUPLICATE CHECK", "newCompanyCode": "", "referenceDocumentnumber": "INV1234569", "documentDate": "********", "postingKey": "01", "accountNumber": "", "customerNumber": "105359", "vendor": "", "amountInDocumentCurrency": "101", "taxCode": "", "paymentTerms": "", "paymentMethod": "", "costCenter": "0002/0002", "profitCenter": "", "assignmentNumber": "Assignment Field Data", "itemText": "2nd DUP CHECK", "orderNumber": "", "networkNumber": "", "purchaseOrderNumber": "", "purchaseOrderItemNumber": "", "quantity": "", "baseUnitOfMeasure": "", "valueDate": "", "busPartnerReferenceKey": "", "assetSubNumber": "", "personnelNumber": "", "businessArea": "", "taxAmount": "", "tradingPartnerId": "", "amountInLocalCurrency": "", "amountInSecondLocalCurrency": "89.15", "assetTransactionType": "", "exchangeRaate": "", "dats": "", "assetValueDate": "", "taxJurisdiction": "", "materialNumber": "", "businessPlace": "", "invoiceNumber": "", "invoiceFiscalYear": "", "invoiceLineItem": "", "referenceKey1": "", "referenceKey2": "", "referenceKey3": "Reference 3 Data", "localCurrency": "", "groupCurrency": "USD", "longText": "This is to test"}}, {"journalEntryItem": {"@sequenceNo": "2", "docType": "MB", "companyCode": "AAJP", "currency": "JPY", "paymentMethodSupplement": "TU", "postingDate": "********", "fiscalPeriod": "02", "documentHeaderText": "2nd DUPLICATE CHECK", "newCompanyCode": "", "referenceDocumentnumber": "INV1234569", "documentDate": "********", "postingKey": "50", "accountNumber": "1010005", "customerNumber": "", "vendor": "", "amountInDocumentCurrency": "101", "taxCode": "", "paymentTerms": "", "paymentMethod": "", "costCenter": "0002/0002", "profitCenter": "", "assignmentNumber": "Assignment Field", "itemText": "2nd DUP CHECK", "orderNumber": "", "networkNumber": "", "purchaseOrderNumber": "", "purchaseOrderItemNumber": "", "quantity": "", "baseUnitOfMeasure": "", "valueDate": "", "busPartnerReferenceKey": "", "assetSubNumber": "", "personnelNumber": "", "businessArea": "", "taxAmount": "", "tradingPartnerId": "", "amountInLocalCurrency": "", "amountInSecondLocalCurrency": "89.15", "assetTransactionType": "", "exchangeRaate": "", "dats": "", "assetValueDate": "", "taxJurisdiction": "", "materialNumber": "", "businessPlace": "", "invoiceNumber": "", "invoiceFiscalYear": "", "invoiceLineItem": "", "referenceKey1": "", "referenceKey2": "", "referenceKey3": "Reference 3 Data", "localCurrency": "", "groupCurrency": "USD", "longText": "This is to test"}}]}}}}}