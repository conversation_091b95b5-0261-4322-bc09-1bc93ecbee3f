{"PostGeneralLedgerRqEnvelope": {"Body": {"postGeneralLedgerRq": {"journalDocument": {"header": {}, "routingInfo": {"sapDocType": "E9E9E9", "dateUpdated": "********", "timeUpdated": "004554", "source": "C2C2C2", "destination": "SAPSAP", "service": "", "globalUniqueId": "1001111111111111111111", "domain": "THIS-IS-C-DOMAIN", "transtype": "BAPIAB"}, "journalEntryItems": [{"docType": "E9", "currency": "USD", "itemText": "test", "postingKey": "40", "sequenceNo": "1", "companyCode": "COM", "postingDate": "********", "documentDate": "********", "fiscalPeriod": "02", "accountNumber": "debit123", "assignmentNumber": "345", "documentHeaderText": "GATEWAY RECEIVABLE", "amountInDocumentCurrency": "*************.00"}, {"docType": "E9", "currency": "USD", "itemText": "test", "postingKey": "50", "sequenceNo": "2", "companyCode": "COM", "postingDate": "********", "documentDate": "********", "fiscalPeriod": "02", "accountNumber": "credit123", "documentHeaderText": "GATEWAY RECEIVABLE", "referenceDocumentnumber": "754", "amountInDocumentCurrency": "*************.00"}]}}}}}