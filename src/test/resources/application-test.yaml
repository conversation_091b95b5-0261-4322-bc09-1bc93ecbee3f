spring:
  datasource:
    url: ****************************************
    username:
    password:
    driverClassName: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
  main:
    allow-bean-definition-overriding: true
  cloud:
    azure:
      servicebus:
        enabled: false
    function:
      definition: consumeAggregatedGeneralLedgerTransactionDetailsMessage;supplyAggregatedGeneralLedgerTransactionDetailsMessage;sendExceptionMessage;consumeAggregatedSettlementMessage;

logging:
  level:
    reactor:
      netty:
        http:
          client: DEBUG

sap:
  decimalFormat: ${SAP_DECIMAL_FORMAT:0000000000000.00}
  documentHeaderText: ${SAP_DOCUMENT_HEADER_TEXT:0000000000000.00}
  postingKeyCredit: ${SAP_POSTING_KEY_CREDIT:50}
  postingKeyDebit: ${SAP_POSTING_KEY_DEBIT:40}
  destination: ${SAP_DESTINATION:SAP}
  source: ${SAP_SOURCE:C2}
  interface: ${SAP_INTERFACE:PI-I-035}
  domain: ${SAP_DOMAIN:PI-I-035}
  service: ${SAP_SERVICE:GL Service}
  transtype: ${SAP_TRANSTYPE:BAPI}
  userId: ${SAP_USERID:********}
  execMode: ${SAP_EXECMODE:S}
  procOption: ${SAP_PROCOPTION:P}

  url: ${SAP_BASE_URL:http://localhost:8080/api/generalLedger}
  connectTimeout: ${SAP_CONNECT_TIMEOUT:125}
  readTimeout: ${SAP_READ_TIMEOUT:125}


ccrecon:
  service-bus-millisecond-delay: 0
  message-retry-count-limit: 3

wiremock:
  server:
    port: 0
    https-port: -1