CREATE schema rec_account;
CREATE schema c2;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

create table if not exists rec_account.aggregate_receivable_accounting_trans
(
    aggregate_id              text                                                  not null
        constraint aggregate_receivable_accounting_id_pkey
            primary key,
    recon_id                  varchar(255),
    post_to_ledger            varchar(16),
    currency                  varchar(100),
    debit_company_code        varchar(50),
    debit_account             varchar(50),
    credit_account            varchar(50),
    line_item_text            varchar(255),
    sub_total                 numeric(15, 3),
    is_processed              boolean                     default false,
    global_id                 text,
    batch_number              integer,
    created_timestamp         timestamp(6) with time zone default clock_timestamp() not null,
    sales_source              varchar(50),
    credit_company_code       varchar(50),
    transaction_type          varchar(50),
    converted_currency        varchar(10),
    converted_subtotal_amount numeric(15, 3)
);

CREATE TABLE IF NOT EXISTS rec_account.receivable_accounting_gl_summary
(
    sap_summary_doc_id      character varying(200) GENERATED ALWAYS AS (encode(sha256(((
        ((TRIM(BOTH FROM sap_doc_number) || TRIM(BOTH FROM company_code)) || TRIM(BOTH FROM local_currency)) ||
        TRIM(BOTH FROM (gl_post_year)::text)))::bytea), 'hex'::text)) STORED      NOT NULL,
    sap_doc_number          varchar(50),
    global_id               text,
    company_code            varchar(50),
    gl_post_year            integer,
    local_currency          varchar(10),
    response_sub_total      numeric(15, 3),
    aggregate_sub_total     numeric(15, 3),
    match_status            boolean,
    usd_converted_sub_total numeric(15, 3),
    exchange_rate           numeric(15, 5),
    created_timestamp       timestamp(6) with time zone default clock_timestamp() not null,
    transaction_type        varchar(50),
    CONSTRAINT sap_summary_doc_id_pkey PRIMARY KEY (sap_summary_doc_id)
);

CREATE SEQUENCE rec_account.receivable_accounting_gl_posting_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS rec_account.receivable_accounting_gl_posting
(
    id                 integer NOT NULL            DEFAULT nextval('rec_account.receivable_accounting_gl_posting_id_seq'::regclass),
    sap_summary_doc_id character varying(200) GENERATED ALWAYS AS (encode(sha256(((
        ((TRIM(BOTH FROM sap_doc_number) || TRIM(BOTH FROM company_code)) || TRIM(BOTH FROM local_currency)) ||
        TRIM(BOTH FROM (gl_post_year)::text)))::bytea), 'hex'::text)) STORED
        constraint fk_sap_summary_doc_id
            references rec_account.receivable_accounting_gl_summary,
    sap_doc_number     varchar(50),
    global_id          text,
    company_code       varchar(50),
    local_currency     varchar(10),
    gl_post_year       integer,
    gl_posting_status  varchar(50),
    request            json,
    response           json,
    created_timestamp  timestamp(6) with time zone default clock_timestamp() not null,
    transaction_type   varchar(50),
    CONSTRAINT receivable_accounting_gl_posting_id_pkey PRIMARY KEY (id)
);

ALTER SEQUENCE rec_account.receivable_accounting_gl_posting_id_seq OWNED BY rec_account.receivable_accounting_gl_posting.id;

CREATE SEQUENCE rec_account.receivable_accounting_trans_details_detail_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS rec_account.receivable_accounting_trans_details
(
    detail_id                  integer,
    document_uuid              character varying(100),
    client_capture_received_id uuid,
    psp_reference              character varying(255),
    reference_id               character varying(200),
    recon_id                   character varying(255),
    post_to_ledger             character varying(16),
    aggregate_id               text,
    pnr                        character varying(50),
    ticket_number              character varying(50),
    amount                     numeric(10, 3),
    payment_type               character varying(50),
    processor                  character varying(50),
    sales_channel              character varying(50),
    transaction_type           character varying(50),
    currency                   character varying(10),
    debit_company_code         character varying(50),
    cost_center                character varying(50),
    merchant_id                character varying(50),
    debit_account              character varying(50),
    credit_account             character varying(50),
    line_item_text             character varying(255),
    is_processed               boolean                     DEFAULT false,
    transaction_date           date,
    issue_date                 date,
    created_timestamp          timestamp(6) with time zone DEFAULT clock_timestamp() NOT NULL,
    payment_processing_path    character varying,
    sales_source               character varying(50),
    credit_company_code        character varying(50),
    card_bin                   character varying(50),
    card_last_four_digits      character varying(4),
    country_code               character varying(2),
    fop_channel                character varying(50),
    station_number             character varying(100),
    refund_id                  character varying(200),
    CONSTRAINT receivable_accounting_trans_details_id_pkey PRIMARY KEY (detail_id)
);

ALTER SEQUENCE rec_account.receivable_accounting_trans_details_detail_id_seq OWNED BY rec_account.receivable_accounting_trans_details.detail_id;

CREATE SEQUENCE rec_account.uatp_aggregate_receivable_accounting_trans_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS rec_account.uatp_aggregate_receivable_accounting_trans
(
    id                  integer NOT NULL            DEFAULT nextval('rec_account.uatp_aggregate_receivable_accounting_trans_id_seq'::regclass),
    aggregate_id        text,
    recon_id            character varying(255),
    post_to_ledger      character varying(16),
    currency            character varying(20),
    debit_company_code  character varying(50),
    debit_account       character varying(50),
    credit_account      character varying(50),
    line_item_text      character varying(255),
    sort_order          integer,
    amount              numeric(10, 3),
    record_type         character varying(30),
    is_processed        boolean                     DEFAULT false,
    global_id           text,
    batch_number        integer,
    created_timestamp   timestamp(6) with time zone DEFAULT clock_timestamp() NOT NULL,
    detail_id           integer,
    sales_source        character varying(50),
    credit_company_code character varying(50),
    transaction_type    character varying(50),
    converted_currency  varchar(10),
    converted_amount    numeric(15, 3),
    CONSTRAINT uatp_aggregate_receivable_accounting_id_pkey PRIMARY KEY (id)
);

ALTER SEQUENCE rec_account.uatp_aggregate_receivable_accounting_trans_id_seq OWNED BY rec_account.uatp_aggregate_receivable_accounting_trans.id;

CREATE TABLE IF NOT EXISTS c2.accounting_status
(
    accounting_status_uuid     uuid                                                  NOT NULL,
    document_uuid              character varying(100),
    client_capture_received_id uuid,
    psp_reference              character varying(255),
    pnr_number                 character varying(100),
    ticket_number              character varying(100),
    amount_capture             numeric(15, 3),
    status                     character varying(100),
    comments                   text,
    issue_date                 date,
    change_timestamp           timestamp(6) with time zone DEFAULT clock_timestamp() NOT NULL,
    currency_code              character varying,
    card_bin                   character varying(50),
    card_last_four_digits      character varying(4),
    refund_id                  character varying(100),
    transaction_type           character varying(50),
    CONSTRAINT accounting_status_id_pkey PRIMARY KEY (accounting_status_uuid)
);
