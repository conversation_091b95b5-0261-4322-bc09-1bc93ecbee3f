TRUNCATE TABLE rec_account.aggregate_receivable_accounting_trans;
TRUNCATE TABLE rec_account.receivable_accounting_gl_summary cascade;
TRUNCATE TABLE rec_account.receivable_accounting_gl_posting;
TRUNCATE TABLE rec_account.receivable_accounting_trans_details;
TRUNCATE TABLE rec_account.uatp_aggregate_receivable_accounting_trans;
TRUNCATE TABLE c2.accounting_status;
ALTER SEQUENCE rec_account.receivable_accounting_gl_posting_id_seq RESTART WITH 1;
ALTER SEQUENCE rec_account.receivable_accounting_trans_details_detail_id_seq RESTART WITH 1;
ALTER SEQUENCE rec_account.uatp_aggregate_receivable_accounting_trans_id_seq RESTART WITH 1;


INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2523, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 1, 645.650, 'DETAIL', true, 'UATP-4107', 1, '2024-12-11 15:53:16.617914+00', 1815863, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2524, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 2, 645.650, 'DETAIL', true, 'UATP-4107', 1, '2024-12-11 15:53:16.618104+00', 1815862, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2525, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 3, 645.650, 'DETAIL', true, 'UATP-4107', 1, '2024-12-11 15:53:16.618112+00', 1815861, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2526, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 4, 645.650, 'AGGREGATE', true, 'UATP-4107', 1, '2024-12-11 15:53:16.618118+00', NULL, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2527, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 5, 645.650, 'DETAIL', true, 'UATP-4108', 2, '2024-12-11 15:53:16.618125+00', 1815860, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2528, 'UATP-1423', '*********', '202404021211_SA', 'EUR', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 6, 645.650, 'AGGREGATE', true, 'UATP-4108', 2, '2024-12-11 15:53:16.618132+00', NULL, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2529, 'UATP-1424', '*********', '202404021211_SA', 'USD', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 7, 645.650, 'DETAIL', true, 'UATP-4109', 3, '2024-12-11 15:53:16.618138+00', 1815859, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2530, 'UATP-1424', '*********', '202404021211_SA', 'USD', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 8, 645.650, 'DETAIL', true, 'UATP-4109', 3, '2024-12-11 15:53:16.618145+00', 1815858, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2531, 'UATP-1424', '*********', '202404021211_SA', 'USD', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 9, 645.650, 'DETAIL', true, 'UATP-4109', 3, '2024-12-11 15:53:16.618152+00', 1815857, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');
INSERT INTO rec_account.uatp_aggregate_receivable_accounting_trans (id, aggregate_id, recon_id, post_to_ledger, currency, debit_company_code, debit_account, credit_account, line_item_text, sort_order, amount, record_type, is_processed, global_id, batch_number, created_timestamp, detail_id, sales_source, credit_company_code, transaction_type, converted_amount, converted_currency) VALUES (2532, 'UATP-1424', '*********', '202404021211_SA', 'USD', 'AA00', '1240130', '1240152', 'C2 Refund Billing', 10, 645.650, 'AGGREGATE', true, 'UATP-4109', 3, '2024-12-11 15:53:16.618157+00', NULL, 'AGENCY_ARC', 'AA00', 'REFUND', 432.450, 'BRL');


INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815469, 'f80a1fe8cd2aaf3bec204390fbff2325719615b5ac1e57a0412afba2916586c3', NULL, NULL, 'EL204BBSP', 'B20241012', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-12', '2024-07-02', '2024-10-12 11:05:27.292361+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815884, '7a22558e571265bc6e885e9c82d28188e2a5227c471a503519c0ffbc46c5b2b7', NULL, NULL, 'TP211BBSP', 'B20241120', '202404021217_SA', 'UATP-1411', 'R9T7CY', '*************', 9.190, 'OA', 'TP', 'BSP', 'SALE', 'EUR', 'AA00', '', NULL, '1240001', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-19', '2024-11-20 16:25:27.684181+00', 'AGENCY', 'BSP_HOT', 'AA00', '100256', '5646', 'BE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815885, 'ed5a939e1ffbbc56fa54e196668119257762bad33b594db35b5f4a04fdf96fbf', NULL, NULL, 'TP211BBSP', 'B20241120', '202404021217_SA', 'UATP-1411', 'J3M8KV', '*************', 2002.200, 'OA', 'TP', 'BSP', 'SALE', 'EUR', 'AA00', '', NULL, '1240001', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-20', '2024-11-20 16:25:27.984187+00', 'AGENCY', 'BSP_HOT', 'AA00', '193954', '9824', 'BE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815641, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 16:44:44.545479+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5056s');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815675, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 22:30:41.842637+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110ec19');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815429, '9392a06f336f45356b1c4d2cc1bf47cdd63393cce8cec47322ceec6441cf109d', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 1452.020, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 13:10:06.687717+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815443, '16637fdaf62636affeead03b0bf3966da9e46e899ad8a4e38be097bcd6882898', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 16:36:33.391458+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815403, '68a99f5c344639b42a8e7c6459dce502205b4c22f2d69f47ec7a1bcd2508d4fc', 'c21dd372-8b30-449f-ad36-18b41a2b7f1f', 'ZSSCZL8TXS5RNS65', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20240930', NULL, NULL, 'REQDGX', '*************', 148.480, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:44:59.052139+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815404, 'acbbbd8bce2575920c692e90edd000934f4e76620b4c4433576de98aa6b6a5f0', '36c2ca3d-dd52-4ec0-87a5-7a81d8e3b4ae', 'C8C5QQDWQTBLZ8V5', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20240930', NULL, NULL, 'TWZTCK', '*************', 423.470, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:45:02.4896+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815405, '2e837593552a85eaa1d950b45e1ff059c7139ddd0397d398ac0cc6a873385cd4', '58814d62-f504-412e-83df-7028cdf5ea32', 'T3DC5B9N89NKGK82', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20240930', NULL, NULL, 'OIENCK', '*************', 2224.700, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:45:06.41919+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815406, 'ba4bb70fb293ba7cbcd8b449c4f50a1e49927615da20e4eeb5f6ffbf24e8e6e8', '108ecf1b-5955-4343-b75d-312c078344ab', 'J3PLNQN92CHFP7V5', 'c41e4f835d0bc8a1ccd933791e606a0fc621b70c78661f7ba72062da0026e9b9', 'B20240930', NULL, NULL, 'TVFGHQ', '*************', 394.480, 'JC', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'JC Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:45:25.675567+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815407, 'c205c40196617254bfdd0c205ec7a7d6b1f19bffe85fa0768c797640cd777adf', '157fc360-ecb2-4d7b-8e44-1aeaf5992038', 'BJG36Z2Q6W5X8N82', 'c41e4f835d0bc8a1ccd933791e606a0fc621b70c78661f7ba72062da0026e9b9', 'B20240930', NULL, NULL, 'TPSHEJ', '*************', 186.480, 'JC', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'JC Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:45:30.376991+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815410, '089df228abe969fcf679ac5e0d02ea2f01300327d5f8c646cf08687f791d8742', '448a66a8-d97a-4db5-aefd-eff1fe4e82db', 'W9JXMX2RKZSKGK82', '86de0aa22847eb93579fb0f6592e50c9fd7e5c331c202d15dfa28c1ad38ad4c2', 'B20240930', NULL, NULL, 'OGIWBC', '*************', 1425.330, 'AX', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240019', '1240071', 'AX Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:49:20.783102+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815411, '6847b41a5fe44e4ae63b9473cb0aaa069ee4e36aab48befc11509d53c6c03396', '13e29885-c80d-4f2f-aec0-93c254f898cc', 'LKPH5ZV8SPSTGLV5', '86de0aa22847eb93579fb0f6592e50c9fd7e5c331c202d15dfa28c1ad38ad4c2', 'B20240930', NULL, NULL, 'SRBUQE', '*************', 540.950, 'AX', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240019', '1240071', 'AX Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:51:21.552702+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815413, '4bae6f2e4068a45f1c5b0daf50bf6d04abf4b7aa6bae0457ad03f5a5a13692ee', '8bf3ef06-8353-4095-b55c-98f04b6dfb12', '****************', '86de0aa22847eb93579fb0f6592e50c9fd7e5c331c202d15dfa28c1ad38ad4c2', 'B20240930', NULL, NULL, 'SVBNMD', '*************', 857.400, 'AX', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240019', '1240071', 'AX Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:52:04.248028+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815414, 'fa866780a36b44dbf68ec74d806b1edbf2439d56bd8271b7523a535546d1161a', '26abd159-8d42-4c6c-aeca-9a65d1795abd', 'XGK8SRPMZTL4C5V5', '5cc18063818f92517ed009598b3ed82dee0a8593a80f9698ab88245a115c54e3', 'B20240930', NULL, NULL, 'PDXKBS', '*************', 436.880, 'DS', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', '', NULL, '1240066', '1240071', 'DS Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:54:42.950656+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815880, '5e04a6c741bedb393cb9fc4706ae708d60cf895d99988413a70c45ca04265e9c', NULL, NULL, 'EL203BBSP', 'B20241120', '202404021217_SA', '1408', 'G1M5SZ', '*************', 265.150, 'MC', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-15', '2024-11-20 16:25:26.957118+00', 'AGENCY', 'BSP_HOT', 'AA00', '249876', '1768', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815644, '3d6776dc783eb5bd604998355683424cda3ca4044a0e59cd1f0f9214d807bd47', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'DNAODC', '*************', 1000.000, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-03-14', '2024-11-06 17:24:13.850669+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '460000', '7845', 'US', NULL, '********', '2bb1e4a67b2a7ef550a25dbd0a70314ef5e6830b29d7d7b3eec86014f9de5397');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815415, '44455fd92d318adb6a2c729fae42daac92b315462694dd26b4e7d91d143aed7b', '12152e6a-c128-4a6a-b123-6eb3fad26529', '****************', '5cc18063818f92517ed009598b3ed82dee0a8593a80f9698ab88245a115c54e3', 'B20240930', NULL, NULL, 'PNMBWW', '*************', 223.480, 'DS', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', '', NULL, '1240066', '1240071', 'DS Co Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 13:57:09.3758+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815895, '115281e269da020a99bdc9f805610bd13c5f2c1be6790781de5dd08d12c52e8a', '781cdc51-e20b-46d6-a0aa-5183b657cd13', 'MPW2NJ8L585QJ2V5', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20241126', NULL, NULL, 'SLIDM1', '*************', 100.000, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-11-26', '2024-11-26', '2024-11-26 19:22:29.445246+00', 'CPS', 'COMPANY', 'AA00', '400062', '0007', 'US', 'STANDARD', '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815660, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 20:34:04.503647+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h6');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815676, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:45:45.888654+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815529, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 15:03:28.415934+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '170d375b195a45c2882296cd5b087f3315ed550b2708132c3e23c131ecb8b692');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815530, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 15:15:22.448246+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '170d375b195a45c2882296cd5b087f3315ed550b2708132c3e23c131ecb8b692');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815677, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:50:11.309271+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0001', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815678, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:50:58.273351+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0002', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815679, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:53:12.422347+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0003', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815680, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:54:06.511199+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0004', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815681, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:54:59.029842+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400007', '0004', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815682, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:55:25.382426+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400008', '0004', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815689, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241107', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-08-09', '2024-11-07 11:17:24.95625+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dwe1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815698, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241107', NULL, NULL, 'RFGTSC', '*************', 453.870, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-23', '2024-11-07 14:21:28.15412+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '05f7d1b37f2e0c5182f40f4dc673ce5b14f890acbfd1e3f26d71f8c05e34a5b3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815702, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 17:47:10.015311+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20510');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815419, NULL, NULL, 'PP9TCRHKWHV75X65', 'MC209LCL', 'B20240930', NULL, NULL, NULL, '*************', 45.090, 'VI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AAAR', NULL, NULL, '1240036', '1240071', 'C2 Sale Local Billing', false, '2024-09-30', '2025-03-20', '2024-09-30 20:37:07.766197+00', 'CPS', 'COMPANY_LOCAL', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815896, 'fdd3384ddf479797666ad1f6d8e847cccdbc8fb958ac3fd3e240085a1d971a39', '9738304a-4a45-4dbd-b088-b8658f420308', 'NDM3JW6QT94LGZT5', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20241126', NULL, NULL, 'IDEMNV', '*************', 200.000, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-11-26', '2024-11-25', '2024-11-26 20:00:44.791262+00', 'CPS', 'COMPANY', 'AA00', '400062', '0007', 'US', 'STANDARD', '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815449, '8a2d41dd262ff4c77dcf4ecd5dedeb490fa7e9ed615670637fea7b5042165f40', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 17:05:30.807783+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815633, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 05:49:42.882397+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5025a');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815646, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 18:13:03.538076+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815647, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 18:17:00.561263+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815648, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 18:19:10.285864+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815649, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 551.010, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 18:21:36.05912+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815468, '35ff41f59875920959c2a0f166b76a736e66e48ef4770076fc3b29d71f6a2e30', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 18:12:44.901101+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815661, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 20:35:41.042015+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc14');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815416, '33d9b2e453f34808611eac829e172658cc58430730722d25af070b655cf7dcbb', '3ab09c6f-**************-f97ecfa3e7d3', 'D49HTX36LZSKGK82', 'fa1dc2d88cba33dc5fa18b6b92f23160993c8b15bf1103a5a575df058ed69c10', 'B20240930', NULL, NULL, 'QVRFOW', '*************', 653.960, 'DI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240121', '1240071', 'DI CO Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 14:10:05.480923+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815683, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 51.910, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:59:03.118995+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0006', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815690, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-08-09', '2024-11-07 11:22:10.394557+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dcd1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815694, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'OIENCK', '*************', 768.450, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-08-12', '2024-11-07 12:56:34.168492+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '67d5a1b04f1e3cf2805bf47e2c019fc7d6f5dbe08c4f735de1a3b5cf1f74260b');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815695, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241107', NULL, NULL, 'RFGTSC', '*************', 453.870, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-23', '2024-11-07 13:01:45.814254+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '05f7d1b37f2e0c5182f40f4dc673ce5b14f890acbfd1e3f26d71f8c05e34a5b2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815699, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 16:58:12.399891+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc205f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815703, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 17:48:34.595299+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20511');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815704, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, 'TKD4PCF4CLR4C5V5', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'OIENCK', '*************', 768.450, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-08-12', '2024-11-07 17:55:20.195108+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '67d5a1b04f1e3cf2805bf47e2c019fc7d6f5dbe08c4f735de1a3b5cf1f742a21');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815707, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 18:44:46.219481+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205g1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815711, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-06-23', '2024-11-07 19:45:48.470517+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400007', '0000', 'US', NULL, '********', '2s7c08b2f3d5a41f6d0e7f5f053dbf0cc4f6d287e01b50f8a3e94cf21fb56c70');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815496, 'fd74998e4ecbcad5ce627967cfc5c60057110f0e5a7786c39c223d068099f6a1', NULL, NULL, 'FD204BARC', 'B20241025', NULL, NULL, 'Z4G7AR', '*************', 1452.020, 'VI', 'FD', 'ARC', 'SALE', 'USD', 'AA00', '', '************', '1240101', '1240052', 'C2 Sales Billing', false, '2024-10-25', '2024-07-02', '2024-10-25 17:51:35.803017+00', 'AGENCY', 'ARC_CAT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815531, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 16:45:42.910995+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '170d375b195a45c2882296cd5b087f3315ed550b2708132c3e23c131ecb8b692');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815417, '0ed15577a1fa4c573ac7d9fdf043f7a8b8e22fea80a1fe21e9d5a6b0f9737e74', 'e3e16b38-9d57-4012-9345-beeb8ff0f865', 'VNGZD84HX75ZGN82', 'fa1dc2d88cba33dc5fa18b6b92f23160993c8b15bf1103a5a575df058ed69c10', 'B20240930', NULL, NULL, 'SIEDGH', '*************', 492.940, 'DI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240121', '1240071', 'DI CO Sales Billing', false, '2024-09-30', '2024-08-09', '2024-09-30 14:11:40.25607+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815480, '080eaedc589cd58feaea1cc3cc3674e610cc8b54ef9a920eea54b4aaf0390a3b', NULL, NULL, 'DS209BARC', 'B20241022', NULL, NULL, 'YXF6TS', '*************', 551.010, 'DI', 'DS', 'ARC', 'SALE', 'USD', 'AA00', '', '***************', '1240121', '1240052', 'C2 Sales Billing', false, '2024-10-22', '2024-07-01', '2024-10-22 04:15:46.466418+00', 'AGENCY', 'ARC_CAT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815634, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 12:13:13.075372+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5025a');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815650, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 19:13:58.513871+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815651, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 19:15:37.53963+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815532, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 16:56:46.235201+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '67ec3b88e624b6aa65b44ecc90603b24e0d4bfe1739b5d3ebb8fceb4f09b90a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815533, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 17:07:21.405319+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '03496fe2c857a342edf21ce3478bf47838b68255b2bf6fd8c46c2e0ced7b4b00');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815652, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'e4372d603ef1c92cc729a5183ad5f0e1bcff83c874fe1f74adc8f165d6b726bd', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'FD', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'DI Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 19:17:50.74176+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc11');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815663, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 21:02:34.897273+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc15');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815684, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 51.910, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 23:59:26.103133+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0006', 'US', NULL, '********', '1ecc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815691, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 12:21:58.214277+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815696, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-06-23', '2024-11-07 13:38:51.235602+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', '1e7c08b2f3d5a41f6d0e7f5f053dbf0cc4f6d287e01b50f8a3e94cf21fb56c71');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815457, 'f09e7e9d9be139829567c28d605f833c0a91c2c5749e15b67c781e665f337bf3', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 17:45:40.057834+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815700, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 17:35:07.808139+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f723-4b8b-9bb2-de445dc205f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815705, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, '****************', 'AX210LCL', 'R20241107', NULL, NULL, 'RFGTSC', '*************', 453.870, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-23', '2024-11-07 18:14:14.662113+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '05f7d1b37f2e0c5182f40f4dc673ce5b14f890acbfd1e3f26d71f8c05e34a5s3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815708, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 19:30:58.256635+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'aecc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815709, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 51.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 19:34:44.355868+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0001', 'US', NULL, '********', 'aecc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815713, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, 'TKD4PCF4CLR4C5V5', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'OIENCK', '*************', 768.450, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-08-12', '2024-11-07 19:58:35.554119+00', 'CPS', 'COMPANY', 'AA00', '304567', '4566', 'US', 'STANDARD', '********', '67d5a1b04f1e3cf2805bf47e2c019fc7d6f5dbe08c4f735de1a3b5cf1f742a21');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815718, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'YSHRYC', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 20:14:33.910101+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205p2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815635, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 12:41:29.935078+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5025a');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815636, '3d6776dc783eb5bd604998355683424cda3ca4044a0e59cd1f0f9214d807bd47', NULL, NULL, 'DI209BBSP', 'R20241106', NULL, NULL, 'K8H3OP', '*************', 1000.000, 'DI', 'DI', 'BSP', 'REFUND', 'USD', 'AA00', '', '**********', '1240119', '1240152', 'C2 Sales Billing', false, '2024-11-06', '2024-03-14', '2024-11-06 12:45:41.780609+00', 'AGENCY', 'BSP', 'AA00', '304567', '4565', 'US', NULL, '********', '5507ddb87f52ac5d6d3780145a626996e09365d50cacc2cac3f81c33ce5c2001');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815543, NULL, NULL, 'FDF36WQBSB7WCCV5', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241029', NULL, NULL, 'KUSHTS', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-10-29', '2024-10-08', '2024-10-29 16:16:10.552327+00', 'CPS', 'COMPANY', 'AA00', '400062', '0007', NULL, 'STANDARD', '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815481, '5202382d8450b45302e6ba4a63f711d4880bf829f35eecea2816d611be67c968', NULL, NULL, 'FD204BARC', 'B20241022', NULL, NULL, 'A6T7MS', '*************', 1452.020, 'VI', 'FD', 'ARC', 'SALE', 'USD', 'AA00', '', '************', '1240101', '1240052', 'C2 Sales Billing', false, '2024-10-22', '2024-07-02', '2024-10-22 09:35:13.465512+00', 'AGENCY', 'ARC_CAT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815653, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 19:41:18.966853+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc12');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815654, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'b2de9256d5bb53b6ba3bba95a57f07fffc6cd9f31f15b03a94d50eaabb8d5c8f', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 19:49:34.200947+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc13');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815655, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 20:01:41.540115+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc16');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815664, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 21:04:50.120412+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110ec15');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815665, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 21:06:34.171857+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'AR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc21');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815666, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 21:08:24.505829+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc17');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815667, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 21:09:41.318053+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815668, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 21:10:17.310743+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815670, '3d6776dc783eb5bd604998355683424cda3ca4044a0e59cd1f0f9214d807bd47', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'DNAODC', '*************', 1000.000, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-03-14', '2024-11-06 21:15:13.673737+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '460000', '7845', 'US', NULL, '********', '432448f501dd2b2b5e53260ce4ad3e55eb68c7fcebfbd825fda50c4323ecfb56');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815672, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 21:18:07.004696+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815673, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 21:23:21.980158+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t6');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815685, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 00:03:25.399109+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815534, NULL, NULL, NULL, 'EL201RFRC', 'R20241028', NULL, NULL, 'SYZBIA', '*************', 1.000, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-10-28', '2024-10-08', '2024-10-28 17:31:13.063971+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '540f56a8423b1ff12392806766fb09563c0b1769323cd9d077fc896836d14332');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815639, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 16:38:49.894893+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5026a');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815640, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 16:40:45.783237+00', 'AGENCY', 'ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c5055s');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815656, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, 'AX210LCL', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 20:13:07.413283+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'AR', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc18');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815657, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 20:28:35.860025+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c505t3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815658, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241106', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-06', '2024-07-01', '2024-11-06 20:31:32.853088+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815674, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241106', NULL, NULL, 'OIENCK', '*************', 1.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-06', '2024-08-09', '2024-11-06 22:16:18.269434+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', '26df80dfc4e831fb2374f7c7f97f4bdac015ec158fb01505c50ed6734110dc19');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815686, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 00:04:08.141822+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'eecc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815687, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.650, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 00:05:06.796336+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815688, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.640, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 00:06:20.199387+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'decc23b793fec18541f8edc4919b4e755a25cf5b687011922110680a57c502h8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815692, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-06-23', '2024-11-07 12:44:33.13345+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', '1e7c08b2f3d5a41f6d0e7f5f053dbf0cc4f6d287e01b50f8a3e94cf21fb56c70');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815478, '485e51f28106630cb3027e9d6d8a24118ea1b2dbb08e9d59fe2b1e11918e3f12', NULL, NULL, 'EL204BBSP', 'B20241014', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240150', 'C2 Sales Billing', false, '2024-10-14', '2024-07-02', '2024-10-14 18:50:44.419255+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815701, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 17:43:38.353302+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815706, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-06-11', '2024-11-07 18:33:35.732211+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20512');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815710, 'c43b95d28200f2713f0d08f2f57eb8a2c7eec1e2b589fd53a16e82075984316d', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'TYHSNV', '*************', 354.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-06-23', '2024-11-07 19:44:24.215725+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', '2s7c08b2f3d5a41f6d0e7f5f053dbf0cc4f6d287e01b50f8a3e94cf21fb56c70');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815714, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, '****************', 'AX210LCL', 'R20241107', NULL, NULL, 'RFGTSC', '*************', 453.870, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-23', '2024-11-07 20:06:54.557536+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'BR', 'STANDARD', '********', '09f7d1b37f2e0c5182f40f4dc673ce5b14f890acbfd1e3f26d71f8c05e34a5s3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815715, 'f7b820c5d2e148e3275f3d2a6e0c18f9b92370e5e8d5fa63541b6c28d03f7b12', NULL, '****************', 'AX210LCL', 'R20241107', NULL, NULL, 'FYTRGD', '*************', 976.450, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-10-19', '2024-11-07 20:07:33.322812+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'AR', 'STANDARD', '********', 'd0b24f1c7f03ec158501e7f5d67f49c0a86e31fb7105c4d837245cfed0f9b1a3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815716, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-06-11', '2024-11-07 20:10:13.675944+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20513');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815717, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'YSHRYC', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 20:11:47.331824+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205g2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815465, 'c2516db22b0695a2b06bd58ee751f99ad1d8fc1ecb1b9e2d3ca7ba4c76d69a3f', NULL, NULL, 'EL204BBSP', 'B20241011', NULL, NULL, 'Z4G7MR', '*************', 756.430, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', false, '2024-10-11', '2024-07-02', '2024-10-11 18:07:03.748037+00', 'AGENCY', 'BSP_HOT', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815719, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'YSHRYC', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 20:17:53.945465+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205p3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815720, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 21:38:30.631226+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'zzcc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815721, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 551.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 21:39:28.787258+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'yycc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815722, '61ff749ef8532b35903eb08d1ff420ed2e5c20cd7780e1259f2ebf3327885a6a', NULL, NULL, 'EL201RFRC', 'R20241107', NULL, NULL, 'ORWLAA', '*************', 501.000, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-07', '2024-07-01', '2024-11-07 21:46:38.525568+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'yycc23b793fec18541f8edc4919b4e755a25cf5c687011922110680a57c505a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815723, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-06-11', '2024-11-07 21:56:30.881086+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20514');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815724, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241107', NULL, NULL, 'YSHRYC', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-07', '2024-05-24', '2024-11-07 22:00:47.319753+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205p4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815725, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241108', NULL, NULL, 'POTJPE', '*************', 455.950, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-08', '2024-06-11', '2024-11-08 05:57:47.953326+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20514');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815726, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241108', NULL, NULL, 'POTJPE', '*************', 455.950, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-08', '2024-08-09', '2024-11-08 07:26:09.521727+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20515');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815727, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241108', NULL, NULL, 'POTJPE', '*************', 455.950, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-08', '2024-08-09', '2024-11-08 07:32:24.4215+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20516');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815866, 'bd481504e91fa39e773b91382fc53025c0646f24da3b8d8b273f18c4eafbbbb6', NULL, NULL, 'DI209BBSP', 'B20241120', '202404021217_SA', '1400', 'K8H3NL', '*************', 551.010, 'DI', 'DI', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-01', '2024-11-20 16:25:24.286412+00', 'AGENCY', 'BSP_HOT', 'AA00', '304567', '4565', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815786, 'd5e9a7f1c3b6e4f2a8c0d7b9e1c4f3a2b5e8d0f7b1c9e3d4a2b7e0c6f9d8a1', NULL, NULL, 'EL201RFRC', '*********', '202404021214_SA', '1124', 'RFZSFT', '*************', 827.140, 'DI', 'DI', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-08', '2024-11-10 13:21:41.986982+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '300006', '0000', 'US', NULL, '********', 'e6c9a3f7b4d2e8f0c1b5a9d7c4e3f8a2b7f1d4e0c9a5b2f6d1e7c3b0a8f4e9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815842, 'c9d7b4e3f1a6c5f0b2a8e4d9f3b6a1e0c3f7b2d1a5c8f4e9b0a7c3e6d8f5', NULL, 'W4F2YZN3L8P5K6X9', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1393', 'RFXDYP', '*************', 759.630, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-02', '2024-11-11 07:33:05.331262+00', 'LEGACY', 'COMPANY', 'AA00', '100145', '4567', 'US', NULL, '********', 'e6f0c9a1d7b3f5e2c4a8b0d1f9c7a5e4d3a2b8f1e0c4b7f6d9a3e2b1c5f0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815871, '7016dd0393a72a811cb06c434d9179ad9ece43882ebd51ec3375f19f560fc779', NULL, NULL, 'DI209BBSP', 'B20241120', '202404021217_SA', '1405', 'D3K8JZ', '*************', 856.060, 'DI', 'DI', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-06', '2024-11-20 16:25:25.220868+00', 'AGENCY', 'BSP_HOT', 'AA00', '386345', '6543', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815739, NULL, NULL, NULL, '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', 'R20241108', NULL, NULL, 'HPDRPL', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240014', '1240071', 'AD Co Refund Billing', false, '2024-11-08', '2024-06-11', '2024-11-08 16:59:55.937709+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '7a4b710b-f723-4b8b-9bb2-de445dc20515');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815816, 'c8f9e2b7a4d5f1a3e0b6c7d9f3b2a1c4e5b0a6c9d8f7b3e1f0d2e9a5c4b3e7a1', NULL, 'GHR9DKY6ZLM4T8B3', 'MC209LCL', '*********', '202404021211_SA', '1141', 'RFPLDK', '*************', 497.120, 'MC', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AABR', NULL, NULL, '1240036', '1240071', 'C2 Sale Local Billing', true, '2024-11-10', '2024-07-02', '2024-11-10 21:03:41.42273+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '234567', '4565', 'BR', 'STANDARD', '********', 'b7d9e4c8a3f5b1e2c0a6f7b2d4e5a9c3e1f0d6b5a4c7d3e9f1e8b0a7c2d6e9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815831, 'd5c3e1f7a8b6f9c0d4a2b7e5f8c1d9a3f0e6b9c7f4a3d1e8b5c2a9f0e7d6a4', NULL, 'QTK9XBL4MRG3C6D2', 'TP206BARC', '*********', '202404021211_SA', 'UATP-1395', 'RFGQNP', '*************', 512.400, 'TP', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', '', NULL, '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-07', '2024-11-10 22:45:10.714819+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100145', '4565', 'AR', 'STANDARD', '********', 'c8f7a3d6e1b9c0a5f2d4b8e7f3a9d5c1e6b0d2f9a4b3c5f1e9a6d8b0f7c3a1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815803, 'f4d7e9b2c3a6f5b1e8c0a123456789abcdef1234567890abcdef1234567890ab', NULL, 'QWR6TXK8MLP4C7N3', 'afc1b9dbb1b6539f2ab497b4f16e6f0d18b0030c099b30bd7d665aa1e54c031b', '*********', '202404021211_SA', '1419', 'RFWZDK', '*************', 2634.210, 'AX', 'AX', 'COMPANY', 'REFUND', 'INR', 'AA00', ' ', NULL, '1240113', '1240071', 'AX Co Sales Billing', true, '2024-11-10', '2024-07-07', '2024-11-10 17:48:40.438078+00', 'CPS', 'COMPANY', 'AA00', '344567', '4565', 'US', 'STANDARD', '********', 'e3d5c9b8a1f7e4c2b6a9f0d8e7b1c3f5a4b2e9c6d0f1b3a7d9e6c4f2a8b5e1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815787, 'a1e8b4c7d9f3a2c5b7e0d6f4a9b1c3e5d8f0a6b2c9e7d3f1b4e2c0a5f9d7e1', NULL, NULL, 'EL201RFRC', '*********', '202404021215_SA', '1127', 'RFAYHB', '*************', 975.630, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-09', '2024-11-10 13:21:59.421416+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'b7d1c9e5a4f3e6b0d8a9c2e4f7b1a5d3e0f6c9b2d4e7a3f8c0a1e9b6f5d2c3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815868, '97ac371b77b06c79b538368878e16c1c427a58a1fa2731032e12961bd98b3f9b', NULL, NULL, 'DS209BBSP', 'B20241120', '202404021217_SA', '1410', 'P9T6QY', '*************', 5453.030, 'DI', 'DS', 'BSP', 'SALE', 'HNL', 'AA00', '', '***************', '1240121', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-03', '2024-11-20 16:25:24.837023+00', 'AGENCY', 'BSP_HOT', 'AA00', '362363', '5643', 'HN', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815804, 'b2a7f1d3e6c9a4b8e5c0d7f9b6e2a123456789abcdef1234567890abcdef1234', NULL, 'LPK9MXT4QVN8F6R2', 'afc1b9dbb1b6539f2ab497b4f16e6f0d18b0030c099b30bd7d665aa1e54c031b', '*********', '202404021211_SA', '1419', 'RFQLDN', '*************', 1789.330, 'AX', 'AX', 'COMPANY', 'REFUND', 'INR', 'AA00', ' ', NULL, '1240113', '1240071', 'AX Co Sales Billing', true, '2024-11-10', '2024-07-08', '2024-11-10 17:48:58.893546+00', 'CPS', 'COMPANY', 'AA00', '374567', '4565', 'US', 'STANDARD', '********', 'a9f0c8d2b7e5f1a3c4e6b9d3a1f7e0c6b4d8f2a5b0e9c7d1b3f4e8a6c5d1e0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815857, 'a51d73b61f3e5d7c20f23e7f5d8c9ab5124e61a37e8f42b0c23d67a4131fdb7', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1424', 'RFVFDG', '*************', 342.560, 'OA', 'TP', 'ARC', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-01', '2024-11-11 18:24:30.27455+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100206', '0000', 'US', NULL, '********', '21ec177a47086b281e150ffy5ad1448e8e65cc53bdc9c3701ya9b55059fc22f');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815869, '7ef5383f249a0ae4b5e513cac5b1bd907f8ac0e6765f3b0cb6b174a7ab524ea0', NULL, NULL, 'DS207BBSP', 'B20241120', '202404021217_SA', '1406', 'L5B2RW', '*************', 54.040, 'DS', 'DS', 'BSP', 'SALE', 'AED', 'AA00', '', '***************', '1240066', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-04', '2024-11-20 16:25:24.957329+00', 'AGENCY', 'BSP_HOT', 'AA00', '600006', '8765', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815870, '78c7f2b99475945f8b29d3b8853b648a8f4e6ea226f3774c59e237e5fcd0a4e2', NULL, NULL, 'EL203BBSP', 'B20241120', '202404021217_SA', '1408', 'C1F9SV', '*************', 2455.050, 'MC', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-05', '2024-11-20 16:25:25.087659+00', 'AGENCY', 'BSP_HOT', 'AA00', '500005', '3754', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815846, 'b9e7f3c2a5d0f8c4b6a1e4d3b2f7a0d5c9a4f1e6b5c8f9e1a2d6c7b0f4a3', NULL, 'F7L9QWY3XN2P8M6T', 'TP211BARC', '*********', '202404021212_SA', 'UATP-1392', 'RFKDLX', '*************', 647.800, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-06', '2024-11-11 07:35:45.141234+00', 'LEGACY', 'COMPANY', 'AA00', '100146', '9876', 'US', NULL, '********', 'c5d7b1f3e4a6c0b9f2a8d9c7e3b5f0d4a9e1f6c3b8f2a5d7e0c4a6b9d2f5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815874, '0c550c3e9309d21e69a300c421e120583eef74b0d1b9a862adfa94cb7ac30c07', NULL, NULL, 'DS207BBSP', 'B20241120', '202404021217_SA', '1406', 'T5R8GY', '*************', 459.090, 'DS', 'DS', 'BSP', 'SALE', 'AED', 'AA00', '', '***************', '1240066', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-09', '2024-11-20 16:25:25.658489+00', 'AGENCY', 'BSP_HOT', 'AA00', '659532', '3453', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815799, 'c5b6a7f8d9e0f123abc4567890123456789abcdef0123456789abcdef1234567', NULL, 'PTC6KRV8BMN4L2P9', 'b1fd9c817884b06c55ee47f35c4773df50c9655ae657d103aaa77d7229459245', '*********', '202404021211_SA', '1420', 'RFDLJV', '*************', 629.540, 'MC', 'EL', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240071', 'EL Co Refund Billing', true, '2024-11-10', '2024-07-03', '2024-11-10 17:47:18.315457+00', 'CPS', 'COMPANY', 'AA00', '224567', '4565', 'US', 'STANDARD', '********', 'e8c1d4b7a6f9b3c5e0d2a8f6c7e5a4b9f3d1b2e7c4f0a5d6b1f8e9c2d7a6b5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815820, 'b2e9c6f8a1d4b3e7c5f1d0a9e3b8c2a4f7b9e0d1c3f6a8b4e7d2c1f9a5e8b0d6', NULL, 'PLM8KRY6NDT5X4V3', 'DS207BBSP', '*********', '202404021211_SA', '1142', 'RFXLDV', '*************', 902.670, 'DS', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240066', '1240152', 'C2 Sales Billing', true, '2024-11-10', '2024-07-06', '2024-11-10 21:06:19.394373+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '604567', '4565', 'BR', 'STANDARD', '********', 'e7b1c9a3d6f4e8b0c2f5a9d8e1b7a4d3c6b9f2d0e3a8c5b6f0a1e9d7b4c3f5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815821, 'd8c5f2b9e1a6c7b4e0f3a8d4b1e9c3f7d2a5c6b8e4f9a0c7d3b6f5a1e8d0b9', NULL, 'TYX5KLG9MPF4V7C3', 'AX210LCL', '*********', '202404021211_SA', '1140', 'RFZDPL', '*************', 2634.210, 'AX', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-07', '2024-11-10 21:11:58.606406+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '344567', '4565', 'AR', 'STANDARD', '********', 'c3f7a1e5d4b9f2a8c0d3e6b4a9d8f1e7b5c6d2a0e9b1f8a4c5f0b7e3d1a6c9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815823, 'f3e8b6d7c2a4b9f1d5e0c1a7f4d9e3b8c5a2d6f1b0e4c3a9d8b7f5c6e2a1d1', NULL, 'MKP4LVX8RN2F5G7C', 'DI209BBSP', '*********', '202404021211_SA', '1139', 'RFZXKV', '*************', 421.890, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', '', NULL, '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-10', '2024-07-09', '2024-11-10 21:18:41.029881+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '304567', '4565', 'AR', 'STANDARD', '********', 'f7d1e8b5a4c9b2e6d3f0a1c7e5b8a6f4b9d2e0c3a7f6b1e9c4d8b3a5f2e6c7d3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815856, '9bf0325ce7a4fd14a75d80c6b0a3f2e82fe7fc15d3e5899be4cf028716a7d3', NULL, NULL, 'EL201RFRC', '*********', '202404021214_SA', '1242', 'RFOUJH', '*************', 878.350, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-09', '2024-11-11 18:23:49.63114+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '3fa5b7c04e719cc26d19020a50cb58489df51e28e7c14y5cyf56d31c90e7ca2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815845, 'e7d3a2b5f4c9f8e0a1d7b6f3c5a4b9e2f0d8c3a7b1e5f9c6a0d2e4f1b3a8', NULL, 'MTY9ZLX5P4Q6F8B7', 'TP211BARC', '*********', '202404021212_SA', 'UATP-1392', 'RFWNZL', '*************', 379.580, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-05', '2024-11-11 07:35:08.520055+00', 'LEGACY', 'COMPANY', 'AA00', '100146', '1234', 'US', NULL, '********', 'f0c8b9a2e5d7c3a4b6f1e9d8a7b2c0d1f4e6b9a5f3d2e8c1a7f5b0c4d9e6');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815813, 'a8d6f9c2e3b1a7d0c4e5f8b6a1d9c3e7f2b0a5f1c8d4b7e9f6c3a2e9d5b4a7', NULL, 'PLM6XRY2F9TK8W4N', '9cf2119199c7ae1df52215a40237e71dcd7d619c283f33d39813967790a0d3da', '*********', NULL, NULL, 'RFHMQT', '*************', 735.890, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240001', '1240071', 'TP Co Refund Billing', false, '2024-11-10', '2024-07-07', '2024-11-10 18:27:57.560042+00', 'CPS', 'COMPANY', 'AA00', '100167', '4565', 'US', 'STANDARD', '********', 'd4e5b2c8f7a9b3e6c1d9f0e4a7b6c3f1d8a5c2e9f3b0d7e6a1f4c5b8e0d3f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815784, 'c8e7b4d0a3f9b6e1d2f5c8a4e9b1f7c0d6a3e5b9f2c7d1a4b8e5c0f3d6b2a9', NULL, NULL, 'EL201RFRC', '*********', '202404021214_SA', '1124', 'RFLOPU', '*************', 615.220, 'DI', 'DI', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-07', '2024-11-10 13:14:51.089151+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '300006', '0000', 'US', NULL, '********', 'b9d2f1e4a7c5d8b3e0a6f7c9d4b1f3e5a8c2b6d0f4e3b9a7c0d1f8e2b6c4a3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815783, 'b6f3e1d7c4a8b9e0f2c5d7a3e9b1c8f4d0e6b7a9c3f1e5b8d4a2f0b6e7c9d8', NULL, NULL, 'EL201RFRC', '*********', '202404021213_SA', '1126', 'RFYTBV', '*************', 289.340, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-06', '2024-11-10 13:14:34.085532+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'd7e1b9c3f5a2d4c8b6f9e0a7c2e4b5d3f8a9e6b1d0c3f7e5b4a8c6d2f9e1b0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815782, 'e2c7f8a0d5e3b6c9f1a7d4b9e5c02f9b3a1e6f7d0c8a9e5b7d4a3f8b2e6c9d', NULL, NULL, 'EL201RFRC', '*********', '202404021213_SA', '1126', 'RFYHTF', '*************', 105.670, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-05', '2024-11-10 13:14:17.783569+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'f1a9c2b3d5e8b7c4e2f1d6a3b9e0c4f7d8e3a6b1c5d2e9f0a7b4c8e5f2b6d3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815865, NULL, NULL, '8M5DL6BT9VQ84QT3R', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', '*********', NULL, NULL, 'YSHRYC', '*************', 40.000, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240021', '1240071', 'AD Co Refund Billing', false, '2024-11-12', '2024-05-24', '2024-11-12 14:21:35.628257+00', 'CPS', 'COMPANY', 'AA00', '751124', '0001', NULL, 'STANDARD', '********', '8a4b710b-f724-4b8b-9bb2-de445dc205po1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815822, 'c6a4b9e2d1f7c3e8a5d9b0f4e3a1b7c8d2f5a0b6e1d3c9f8b7e0a4d6b5c1f9', NULL, 'GLK9MPT8QVF6W4R2', 'AX210LCL', '*********', '202404021211_SA', '1140', 'RFPWZK', '*************', 1789.330, 'AX', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-08', '2024-11-10 21:12:41.810125+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '374567', '4565', 'AR', 'STANDARD', '********', 'e9b7c4f2a8d6b3e5c1a4f0b9e7d8c2f6a3b1e0c5f8d4a6b7c9e2f1a5d3e8b0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815829, 'a9f1d2c7b6e5a3f0c8e0b4f7d3a9c5b6e1d4a7c2f8b0e5d3c9f6a3b7e1d0f4', NULL, 'PTY5XZL3BFG8C2D1', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1395', 'RFDJXN', '*************', 973.180, 'OA', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', '', NULL, '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-06', '2024-11-10 22:18:41.109454+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100267', '4565', 'AR', 'STANDARD', '********', 'e3f8a7b9d0c6b2f1d4a5e0c7a1f3d9e6b4c1f7b2d9a8c5f6e3a4c0d1b7f2e8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815850, 'f35d62be72c0e1d2a87f0e98ba21f3a78ec2f50bf7048dbe5f34c129b50fa7e', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', '1241', 'RFEDFT', '*************', 846.560, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-03', '2024-11-11 18:22:07.967281+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '2c90edb574501ca75e4c16918c8f5ff92e58c2110y5y43b07137c0d986ae1fc');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815797, 'a1b2c3d4e5f6789abc01234567890def1234567890abcdef1234567890abcdef', NULL, 'QTD3PCM5CKG2C7V1', 'e4372d603ef1c92cc729a5183ad5f0e1bcff83c874fe1f74adc8f165d6b726bd', '*********', '202404021211_SA', '1421', 'RFZJHU', '*************', 485.760, 'VI', 'FD', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240119', '1240071', 'FD Co Refund Billing', true, '2024-11-10', '2024-07-01', '2024-11-10 17:46:46.792147+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', 'b4d6e8f908c3d21b6f1a4b78e5c9a123d1e7f5b9a8c0d3e4b2a6f5c7e8d9a4b1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815798, 'f1a2b3c4d5e6789abc01234567890def4567890123456789abcdef1234567890', NULL, 'RKM9PQR6TYF4C8L2', 'e4372d603ef1c92cc729a5183ad5f0e1bcff83c874fe1f74adc8f165d6b726bd', '*********', '202404021211_SA', '1421', 'RFGLPK', '*************', 376.890, 'VI', 'FD', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240119', '1240071', 'FD Co Refund Billing', true, '2024-11-10', '2024-07-02', '2024-11-10 17:47:02.130792+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', 'd3e4f5b9a8c1d67b2a9f0e5c7b6a4d1c8f9e7d5a0b6f2e4c3d8b7f1e9a6c2b5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815854, '3f20d5e2b07f1c8a84ec7b06d2f9f9e5b35fa7480ea21cb58f760e9d2a43ef', NULL, NULL, 'EL201RFRC', '*********', '202404021213_SA', '1244', 'RFLOUJ', '*************', 546.650, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-07', '2024-11-11 18:23:16.833585+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'd042e9c1e5485f7732109ac8y4f5eb5ycb3c06e7d1c2511f89607d24fe6a5c0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815832, '6f5b8d4a7e9c1b2d3e4f0a3b9d7c5e1a2b3f6a8c0d2f4e5a7c8b6f9e1d0a3c4', NULL, '5G2LFN8P6QX3V4Y1', 'EL201RFRC', '*********', '202404021211_SA', '1147', 'RFZBTU', '*************', 482.130, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-01', '2024-11-11 05:48:52.710654+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '8f4a9c2d6b3e5f0c1a7b0d2e9f5a6c7d3f8b9a4e2c5f7d0b6a1e3c4b9d8f2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815877, '067a0b3a3fe0f09ef4da3bd5766be237e9a7b45ba5988297b3e2ad830f1efc0a', NULL, NULL, 'EL204BBSP', 'B20241120', '202404021217_SA', '1408', 'S8N3DY', '*************', 62.120, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-12', '2024-11-20 16:25:26.440561+00', 'AGENCY', 'BSP_HOT', 'AA00', '499975', '2346', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815828, 'c6f8b3d9e4a1f7b2a9e0d5c7a3b6c4e1f5d3b8f0c2a9f1e7d5c4a3e8b0d9c6', NULL, 'NFK7YZG6LTD5C4M8', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1395', 'RFWZDV', '*************', 323.750, 'OA', 'CB', 'COMPANY_LOCAL', 'REFUND', 'ARS', 'AA00', '', NULL, '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-05', '2024-11-10 22:18:23.520366+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100277', '4565', 'AR', 'STANDARD', '********', 'b8d1f5e4c3a7b2f9d0c6f3a4e5b9c1d7f4e8a6b1d3c5a9f0e2b4c7f8d9a5b6');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815847, 'd2f9b7a5e8c4d3b1f0c7a6d4e2b9a1f5c8e3a9d0b6f4a3e7d5b1c8f0a2', NULL, 'G2Q8XKL4MY3P5N6F', 'TP211BARC', '*********', '202404021212_SA', 'UATP-1392', 'RFMCBD', '*************', 215.300, 'OA', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '***************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-07', '2024-11-11 07:36:12.045386+00', 'LEGACY', 'COMPANY', 'AA00', '100254', '6543', 'US', NULL, '********', 'a7f5d2c9e0b1f3a6c4d8b9f2e6c3a5b0f7d4c2e8b1f0a3d6e4b5c9d1f8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815878, 'ebbf4e81a888e63c1dc11babc034957b65752afe45e9b88174261c8b8f3ff35c', NULL, NULL, 'DS209BBSP', 'B20241120', '202404021217_SA', '1410', 'F2P7JX', '*************', 3.130, 'DI', 'DS', 'BSP', 'SALE', 'HNL', 'AA00', '', '***************', '1240121', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-13', '2024-11-20 16:25:26.632544+00', 'AGENCY', 'BSP_HOT', 'AA00', '367753', '8760', 'HN', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815879, '70dfcf2c56a287896872470d039874520a9352c320c8b0881cdbf7623a9fcc14', NULL, NULL, 'DS207BBSP', 'B20241120', '202404021217_SA', '1406', 'W6T9RV', '*************', 8464.140, 'DS', 'DS', 'BSP', 'SALE', 'AED', 'AA00', '', '***************', '1240066', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-14', '2024-11-20 16:25:26.797166+00', 'AGENCY', 'BSP_HOT', 'AA00', '699999', '9564', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815833, '1e3d4b9c7a5f0b2e6f8c9a0d2e5a3c4b6d7a1f9b8d0c5f6a2e4b7c3f9a6d0e', NULL, '8G4MLN7WQZ3H5V9T', 'EL201RFRC', '*********', '202404021211_SA', '1147', 'RFBLJT', '*************', 562.410, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-02', '2024-11-11 05:52:22.960124+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '5a6f1d8e0c3b9a4f2e7b0d9f4c6a1b3d5f9c7a8e2f4d6b0a3c5e7b8f9d0c2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815890, 'aec3a0128aad7d876cb063ad47c63d3dd202a0929fa26e347e76059196335db6', 'f3b18e70-b8bb-49c7-8ca6-b75552fe86ca', 'HKSMT2C7HX9QJ2V5', '323e0a136ba6d12ddaefcdcdeb49c6efcd223dfff3a456336508db05d17cd259', 'B20241125', NULL, NULL, 'TWOFV5', '*************', 100.000, 'VI', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', ' ', NULL, '1240014', '1240071', 'AD Co Sales Billing', false, '2024-11-25', '2024-11-10', '2024-11-25 17:38:19.666057+00', 'CPS', 'COMPANY', 'AA00', '400062', '0007', 'US', 'STANDARD', '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815841, 'b1c4e9d3f6a8b7c2e4f5d1a6c0b3f9e7d5a8c2e1b6f4a7d9e0c3b5f8d4a6', NULL, 'P6Z1NXK9Y3L7F5M4', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1393', 'RFWKLM', '*************', 498.750, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-01', '2024-11-11 07:32:11.180091+00', 'LEGACY', 'COMPANY', 'AA00', '100145', '7890', 'US', NULL, '********', 'a9b2d7f1c3e6b5a8d4f0c1b9f3e7a2d8c5a0f6e4d3b8c9f2a1e4d7b5f0c6');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815807, 'd2c9e7f1a4b5d6c8e3f0a9b8f4e1c3a7b6d9f2e5c1b3a8f7d4e9b2c6a1e5f0', NULL, 'XTL5PVR9WYM4C8F6', '8f5b685318d05c5ee9cff5d9a9edf3a34641c5fb5732c9829a1eeb8502cf7db2', '*********', NULL, NULL, 'RFUYTB', '*************', 432.670, 'OA', 'TP', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240009', '1240071', 'OA TP Co Refund Billing', false, '2024-11-10', '2024-07-02', '2024-11-10 18:24:16.28406+00', 'CPS', 'COMPANY', 'AA00', '100267', '4565', 'US', 'STANDARD', '********', 'f4e9c7b1d2a5e3b6c0f8a9d3e5b7f1c4d6e8b2f3a7c9d0e6a1f2b4c5e9a8b3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815809, 'b7f0e8c5a1d3b9e4c6a2f9d8e1c3a4b6d7e5f1c0a9b2d4e7f3b8c1e6a9f2d5', NULL, 'QYM3PWL7CVF8R6X2', '8f5b685318d05c5ee9cff5d9a9edf3a34641c5fb5732c9829a1eeb8502cf7db2', '*********', NULL, NULL, 'RFLQAD', '*************', 698.540, 'OA', 'TP', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240009', '1240071', 'OA TP Co Refund Billing', false, '2024-11-10', '2024-07-03', '2024-11-10 18:26:42.913224+00', 'CPS', 'COMPANY', 'AA00', '100267', '4565', 'US', 'STANDARD', '********', 'c9e2f5b3a8d1e4c7b6f0d3a5e7f8c1b9a2d4e6c0b1f3a7d9b8c2e5f1a6d7e3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815815, '8fa1c3b4e2f5d6c7a9b0d3f8a6b1e7c4f2d9e0a1c8b5f2d3e9a6c4b1f8d0e3a9', NULL, 'PTK8YZW6MGF1X3L9', 'MC209LCL', '*********', '202404021211_SA', '1141', 'RFBWJM', '*************', 587.340, 'MC', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AABR', NULL, NULL, '1240036', '1240071', 'C2 Sale Local Billing', true, '2024-11-10', '2024-07-01', '2024-11-10 21:01:13.245435+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '224567', '4565', 'BR', 'STANDARD', '********', 'a9c4f8b7d1e5b6a0c3f7d8e9f2a6c4d0b1e9c3a7b5d6f2a4e8b1f0d3c9e7a5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815858, 'b23e47c91d8b6e5f01a92b4f7e8c1a8320f64b707c9f82a0f34e27d8154bac6', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1424', 'RFQMXT', '*************', 386.340, 'OA', 'TP', 'ARC', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-02', '2024-11-11 18:24:53.713659+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100306', '0000', 'US', NULL, '********', '1cff82a101ffe40a7e7572cbyac3y1bce55b62c599581d255d8c8690704431e');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815860, 'e41d67f82a6e4c5f03c92b1f7d8b3a9124f63d709e8f42a7b15e47c3136aec5', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1423', 'RFYSNT', '*************', 867.450, 'TP', 'TP', 'ARC', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-04', '2024-11-11 18:25:35.960482+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100106', '0000', 'BE', NULL, '********', 'd490c575a71a91cc8a525yeb5fc6fce58b115d1c634049eeybf230272871f80');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815775, 'c7e4b3a8f5d9c2e1f0b6a1e7f4b8c5a9d1e3f6b0c8d4a2f9e7b5c6f1a0d9e2', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1388', 'RFUVCD', '*************', 912.230, 'TP', 'TP', 'BSP', 'REFUND', 'EUR', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-04', '2024-11-10 13:04:54.536903+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100106', '0000', 'BE', NULL, '********', 'a9f4c2b1e3d5a8b7c4f0e9d7b6a3c8f2e1d4f7b0c6e5a1b9d3f8c2e0b4f7d1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815843, 'f8d6b3c7a4e5b1d9a0c3f9e4a7d1c5e8b2f3a9d0e6b4f1a3d7c9e5f2b0a6', NULL, 'W8L3YK2X5M9F7T4Q', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1393', 'RFLDZK', '*************', 284.500, 'OA', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '***************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-03', '2024-11-11 07:33:39.572328+00', 'LEGACY', 'COMPANY', 'AA00', '100254', '2345', 'US', NULL, '********', 'd9e1f3a5b6c7a2f8b4d0e6c9a3f7d5c4b0f1e2a7c9d8b3f0a5e4d6c1b8f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815834, '9c2b4a1f5d8e6c0b3f7a9e1d4c5f0a3e7d2b6a8c3e5f7d0b6a9e8c4f1d3b7a5', NULL, '7J1MLQ3PX8F6V4Y5', 'EL201RFRC', '*********', '202404021212_SA', '1144', 'RFJKMS', '*************', 693.290, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-03', '2024-11-11 06:33:04.158332+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '7f2c4d9b8a1f0e6c3b7a5d0f9e4c2b1a3d5f9c8e7b6a3d4f5e0b9a2c6f1d3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815851, '789f41bf32c72a50e5c8f06de2a1282b33d97fa5f4e1c0bf680ee3d52a79f0', NULL, NULL, 'EL201RFRC', '*********', '202404021212_SA', '1245', 'RFHYTG', '*************', 345.650, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-04', '2024-11-11 18:22:27.146473+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'ac921cf7710e8fbd0c9c5y4y116a82c05302fb9758f5e316c9ed704b1e2c5d5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815817, 'd1e9c3a5f2b6d4e7b9c0a6f3e8b4a7d5e1c9b2a0f7e5d3c8b1f6a4d7e0b5a2c9', NULL, 'QDM6YRK7FTW8P4N9', 'DI209BBSP', '*********', '202404021211_SA', '1138', 'RFJGLV', '*************', 723.480, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-10', '2024-07-03', '2024-11-10 21:05:17.491668+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '364567', '4565', 'BR', 'STANDARD', '********', 'f5d2c4b8a9e0d3f6b7a2c9e4b3f1d8e7c6a0b1d4f9e2a5c3e8b6a7c0f5e9d1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815852, 'e7c2b03fd4517a82f980b5ea6fc4f32e0da983715d80ebf2f604d2c5a3fb9e', NULL, NULL, 'EL201RFRC', '*********', '202404021212_SA', '1245', 'RFIUYH', '*************', 97.340, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-05', '2024-11-11 18:22:44.274649+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '185751bb20fc574610c4d3y5c23y5a986e90e27eac5981410c9f2e1d8cf74d3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815805, 'c1e7b5d8f9a6c4b3e2a0d1f8e7b9f3c5d2a6b4f0e3c8a7d1e4b9f6c2a5e8d3', NULL, 'MKP4LVX8RN2F5G7C', '8791f7c51b077a882e2142ae5163b723e07fa75b0083b1fb550cdea72636265d', '*********', '202404021211_SA', '1418', 'RFXCDL', '*************', 421.890, 'JC', 'AD', 'COMPANY', 'REFUND', 'GBP', 'AA00', ' ', NULL, '1240119', '1240071', 'JC Co Refund Billing', true, '2024-11-10', '2024-07-09', '2024-11-10 17:49:16.848672+00', 'CPS', 'COMPANY', 'AA00', '354567', '4565', 'US', 'STANDARD', '********', 'f7d1e8b5a4c9b2e6d3f0a1c7e5b8a6f4b9d2e0c3a7f6b1e9c4d8b3a5f2e6c7d9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815818, 'e5c8b3d9a4f7e2b6d1c9a6f3e0b7d4c2a1f8b5e9c7d0f3a2b4e1c6a9d5f0b8', NULL, 'TVG4XPK7QLM9F2W3', 'DI209BBSP', '*********', '202404021211_SA', '1138', 'RFTQLW', '*************', 841.350, 'DI', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-10', '2024-07-04', '2024-11-10 21:05:27.381417+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '364567', '4565', 'BR', 'STANDARD', '********', 'a2d4b7f9e1c6a8d3e5f0b1c9a3f7d6e2b5c8f4e0d7a9c2f6b3e1a5c4d8b0e9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815819, 'f9a7b8c1d6e4f3b2a5c0d9e8b4f2a7c3e1d5f6b0e2c8a9d3b6f1e9c7a4d5b2', NULL, 'QLN2MGR8YXP5F7V3', 'DS207BBSP', '*********', '202404021211_SA', '1142', 'RFMWZL', '*************', 621.940, 'DS', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240066', '1240152', 'C2 Sales Billing', true, '2024-11-10', '2024-07-05', '2024-11-10 21:05:53.222706+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '604567', '4565', 'BR', 'STANDARD', '********', 'd4c3f7a6e1b9f0a8b2e7d5c1f6a3d8e4c9b0f5e2a7c6b1d3e8f9b4a5d0e1c2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815781, 'f4a8b6e92c57d3e1a3f89c2b1e7d06c4b39e5f8c2a6d74b1c0e9d5a2f4a8b7d', NULL, NULL, 'EL201RFRC', '*********', '202404021212_SA', '1125', 'RFBHFT', '*************', 419.570, 'DI', 'DI', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-04', '2024-11-10 13:13:58.79114+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '300006', '0000', 'US', NULL, '********', 'b67a9c2f5d4e8b7c0e1f2b3d9a7e0c4f8a9e3d5b6f1c2b3d7e9c0e5b4a2f8e1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815824, '9bf2e4a3d7c8b0f1a6e2d5f9c3a1b4d0f7c5a9e6b3d1f8a4e9c7d2b1f6c3a0', NULL, 'PTK8YZW6MGF1X3L9', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1394', 'RFGBLD', '*************', 587.340, 'TP', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240009', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-01', '2024-11-10 22:14:01.093324+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100145', '4565', 'BR', 'STANDARD', '********', 'a9c4f8b7d1e5b6a0c3f7d8e9f2a6c4d0b1e9c3a7b5d6f2a4e8b1f0d3c9e7a5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815806, 'e1f9a8b7d6c5e4a3b2d0f7c1e3b4a9d8c7e6f0b9d2e4c8f1a5b3d6e9f2c7a4', NULL, 'TYG9KPV7CMX6D3F4', '8f5b685318d05c5ee9cff5d9a9edf3a34641c5fb5732c9829a1eeb8502cf7db2', '*********', NULL, NULL, 'RFHKSL', '*************', 547.210, 'OA', 'TP', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240009', '1240071', 'OA TP Co Refund Billing', false, '2024-11-10', '2024-07-01', '2024-11-10 18:23:32.513341+00', 'CPS', 'COMPANY', 'AA00', '100267', '4565', 'US', 'STANDARD', '********', 'c8e7a4f2d9b5f1e6a3b9c0d4e5f7b2a1d6c9f3e8b7a5c1e0d2b4f8e3c6a7b9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815776, 'a9d5e4f1b8c7f0b6c2e3d1a8f6b9c4e0f1b3e7a9c6d8f2e1b4c9a7d0e5f3c8', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1388', 'RFMKLP', '*************', 78.120, 'TP', 'TP', 'BSP', 'REFUND', 'EUR', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-05', '2024-11-10 13:05:16.786263+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100106', '0000', 'BE', NULL, '********', 'b8c2a1f7d4e6b0c9a5e9f3d2b7f1e4a3d6c5b9a7f0e1c4d2f8a6e3b1c9d5f0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815777, 'f7d3a9c5e2b4f8c1d6e0b3a7c9f1e5d2a4b8f6c0e3d7b1f9c2e4a6f0b5d9c8', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1388', 'RFQLMN', '*************', 459.880, 'TP', 'TP', 'BSP', 'REFUND', 'EUR', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-06', '2024-11-10 13:05:48.162375+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100106', '0000', 'BE', NULL, '********', 'e7a9f6b4c2d1e3b0f5c8d9a3b7f2a6c4e0b8f1d7c5a3e6b9d2f4c1b0e7f9a5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815825, 'b1e4c7f8d2a9c0e6f4d8b3a7f5c2e0d9b6a1c4e7f9b2a8c3d5f1b7a6e3c9d4', NULL, 'MRK4BYZ8TLF9D3P2', 'TP211BARC', '*********', '202404021211_SA', 'UATP-1394', 'RFZXLD', '*************', 698.250, 'TP', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240009', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-02', '2024-11-10 22:14:51.212209+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100146', '4565', 'BR', 'STANDARD', '********', 'e2b5f9c8a7d4e0a3f6c1d7b2e9a4f3b6c9d0f7e1a5b8c2d6f4e3a1b7c6d9f5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815811, 'c6e9b3d2a5f4c7e1b8d0a9b6f3e2d1c5a8f0e7b4a1d9c2e3b7f8a6d4e0b5f1', NULL, 'KVP6QMN8FTL4Y2W3', '9cf2119199c7ae1df52215a40237e71dcd7d619c283f33d39813967790a0d3da', '*********', NULL, NULL, 'RFQTPL', '*************', 549.880, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240001', '1240071', 'TP Co Refund Billing', false, '2024-11-10', '2024-07-05', '2024-11-10 18:27:21.83859+00', 'CPS', 'COMPANY', 'AA00', '100167', '4565', 'US', 'STANDARD', '********', 'b6d1e8f3c2a5f7b9e0c4d9a7b2e6c5f8d1a3f0e7b4a8c3d2e9f5a1c6b0e4d3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815800, 'd9e1b2f3a4c5d6e7890abc1234567890123456789abcdef0123456789abcdef0', NULL, 'YTV2PXG6LKD3F5N8', 'b1fd9c817884b06c55ee47f35c4773df50c9655ae657d103aaa77d7229459245', '*********', '202404021211_SA', '1420', 'RFVYXW', '*************', 738.210, 'MC', 'EL', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240071', 'EL Co Refund Billing', true, '2024-11-10', '2024-07-04', '2024-11-10 17:47:34.398435+00', 'CPS', 'COMPANY', 'AA00', '254567', '4565', 'US', 'STANDARD', '********', 'c7e2d5a9b8f1c3d0e4a6b7f9e5c8a2d1f0e3b9d6a5f4c2b7e1f8a9d3b6c4e0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815892, 'b6af5a86d4814742b4dd72cb5291dc9163d8ecef53b49d406b51212ac3a6c920', NULL, NULL, 'DS209BARC', 'B20241126', '202404021217_SA', '1407', 'X7F2QA', '*************', 551.010, 'DI', 'DS', 'BSP', 'SALE', 'USD', 'AA00', '', '***************', '1240121', '1240052', 'C2 Sales Billing', true, '2024-11-26', '2024-07-01', '2024-11-26 05:22:47.155657+00', 'AGENCY', 'ARC_CAT', 'AA00', '304567', '4565', 'US', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815802, 'b1f0e9c8a7d6b5e4f3c2d1a4567890abcdef1234567890abcdef1234567890ef', NULL, 'PLG8XRK6MDF3C9V2', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', '*********', '202404021211_SA', '1422', 'RFEDCK', '*************', 902.670, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240021', '1240071', 'AD Co Refund Billing', true, '2024-11-10', '2024-07-06', '2024-11-10 17:48:20.552545+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', 'b8f1e4c3d9a5f7b6c0e2d4a1b7e3f9d6c8b2a5e7f0c1d4a9b3f2e6c7a1d5b0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815855, 'f8b7ce3f0f21d09ae5ab34e58c726f320fb1ad4fc950b278a1d96e502af7c3', NULL, NULL, 'EL201RFRC', '*********', '202404021214_SA', '1242', 'RFTYVB', '*************', 743.450, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-08', '2024-11-11 18:23:32.884281+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '5c8a5d14c3b2c7c90e7e015f316e4d529a9ef5yb1071fy28ed0b463c1d75882');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815835, '8f1c4a2d9b5e0b7c3a6f9d1e3c5b7a2d6e4f8c0b1a9e7f3d4b2c5f8a0e3d1b6', NULL, '1T5LXP7QVF6N3G9D', 'EL201RFRC', '*********', '202404021212_SA', '1144', 'RFLSNW', '*************', 523.670, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-04', '2024-11-11 06:33:25.43095+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '9e0d1b3f5a7c8d2e6b4a9f0c7b6a5e3d4f2c8b1a3d6f9e0c1b7a4c9e2d5f8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815839, 'f8d1a9c6b7e3f0d2c5b4a8e0c9f7b3a5e2d6f1c3a7b5d4e6c1a9e3d0b8f2a4', NULL, 'Q8T3LFY2KX9M7V4G', 'EL201RFRC', '*********', '202404021214_SA', '1143', 'RFGPZL', '*************', 671.350, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-08', '2024-11-11 06:44:08.229531+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '9e1b3a7f4d6c2f8e5b0d3f7a4c6e9d0a2f5b8c1a4d6b3e7f1a9c5e0b2d7f3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815840, 'b0f9a8d3c5e4b6f7c1a9d4e8f2b3a5c0d6e7f4b1a2c8d0f3e9b7a4f5c1d6e3', NULL, 'G2M8QYN7X5K3L4D9', 'EL201RFRC', '*********', '202404021215_SA', '1145', 'RFXTQL', '*************', 385.250, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-09', '2024-11-11 06:46:23.22127+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', 'a3d7f1c5b9e2a6f8c4d0b2f3e6a1d9c7e0f4b1a5c9d3e8b7a2f0d6e5c1b4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815778, 'b6f7a1d8e2c9b4f3a0d5c1e9f8b2a7c4d3e1f6b0c8d9e4f2a3b7f5c0e6a1b3', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1388', 'RFZXYQ', '*************', 623.540, 'TP', 'TP', 'BSP', 'REFUND', 'EUR', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-07', '2024-11-10 13:06:07.325657+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100106', '0000', 'BE', NULL, '********', 'a4e7c2f1b5d8a9f6b0c3d9e4f1b7a2c8e0f3b9d5c4e1a6f2b3d8c9a7f0e6b1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815772, 'd8e4f5c9a1b6e0f3c7b9a2d1e7f4b5a7c9e2d4f1b3c6a8f0d1e7b2c4a9f5b0', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1389', 'RFAXYP', '*************', 312.450, 'OA', 'TP', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-01', '2024-11-10 13:03:03.949682+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100206', '0000', 'US', NULL, '********', 'c4d5e6b7a2f3c9e1d7b0f8a4c1e9b7a3f5e6d4b8a2c0f1d9e7b3a6f4d1c2b8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815826, 'd4a1f3b5e8c9a2f6d7b0e4c1a5f9b6d3c7e0a4f8b2d1e6c9f5a3b7d9c0e2f4', NULL, 'PTK3YRW6MGF5X7D4', 'TP206BARC', '*********', '202404021211_SA', 'UATP-1394', 'RFPLJM', '*************', 476.100, 'OA', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240009', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-03', '2024-11-10 22:17:32.33443+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100254', '4565', 'BR', 'STANDARD', '********', 'c7f4e1a5d8b3f0e9b6c1a9f5d3e7a0c2f8d9b4a7c5e3d6b2f1c8a0e6f4b5d2');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815827, 'a3f2d5c9b4e1f7b6a8c0e2d9f3b6a5f4c7d1e0a9b8c6f2d1e5c4a0b7d9f8a3', NULL, 'PRT9YLG2CDF6X5D3', 'TP206BARC', '*********', '202404021211_SA', 'UATP-1394', 'RFQLND', '*************', 812.900, 'OA', 'CB', 'COMPANY_LOCAL', 'REFUND', 'BRL', 'AA00', '', NULL, '1240009', '1240052', 'C2 Sales Billing', true, '2024-11-10', '2024-07-04', '2024-11-10 22:17:53.007137+00', 'CPS', 'COMPANY_LOCAL', 'AA00', '100256', '4565', 'BR', 'STANDARD', '********', 'e5f1c9a4d6b3f0e7b8c1a7f5d4e2a6f3c9d0b2e4f8a3c7d5f6b1e9a2c4d0b8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815801, 'a9b8c7d6e5f401234567890123456789abcdef0123456789abcdef1234567890', NULL, 'KMV4PNF7YXW5G2C8', '2c27189c14bc04d521640ba2b7d069a7451d220afd6b2d9db049c6d5433b8aaa', '*********', '202404021211_SA', '1422', 'RFJXNM', '*************', 501.350, 'VI', 'AD', 'COMPANY', 'REFUND', 'USD', 'AA00', NULL, NULL, '1240021', '1240071', 'AD Co Refund Billing', true, '2024-11-10', '2024-07-05', '2024-11-10 17:48:04.547482+00', 'CPS', 'COMPANY', 'AA00', '304567', '4565', 'US', 'STANDARD', '********', 'd0e9b4f2c5a1d8b7f3e6c7a9d2b1e4f5a3c8d0b6f7e9a2c4b5f1d3a6e8c7b4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815773, 'e6a5b8f1d2c9a7e4f0b3d1c4e9b8f3a2d4f7e1c9a0b5f2e3d7c6a8f1e5b0c9', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1389', 'RFKPLM', '*************', 419.880, 'OA', 'TP', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-02', '2024-11-10 13:04:09.783868+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100306', '0000', 'US', NULL, '********', 'd9e7f1c4a2b8f6e3d5c0b7a9e8f3c2d4b1e9a7f6b3c1d4e8f2b5a0c7e9d3b1');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815774, 'f9d2e7b4c8a1f3b6e0c5a9d7f1c4b3e6d0a8f7b9c2e5f1d4a3c6b7e0f9a4d2', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1389', 'RFNJRT', '*************', 562.340, 'OA', 'TP', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-03', '2024-11-10 13:04:26.991375+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '100406', '0000', 'US', NULL, '********', 'b8d1f7a3c4e9f2b0a6c5e3d7f1a8b9c4d2e5f6a3b0d9e7c4f1b6a9d8c3e0f5');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815853, 'e7d0f5231a6fb9eaf8cb28c4e2f0d18b53270a8f0a3dc976ebfe14f502c9b6', NULL, NULL, 'EL201RFRC', '*********', '202404021213_SA', '1244', 'RFBVGF', '*************', 324.440, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-06', '2024-11-11 18:23:00.294948+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '56c750ab9e38ecdfc245712y5y8e1f91b40460f0c118d2939e5a71d2c547e0d');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815849, 'b2f7e431a25df68890f7cd50b203f8a58e2ecf3d12f9e71c805f04ba377d6c9', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', '1241', 'RFGHBG', '*************', 765.010, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-02', '2024-11-11 18:21:49.854899+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', '54190a7cf875e0b5c5129c8f21e3d46b7c2c51510e4c9d1a982y5y73fc01668');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815779, 'f2a9b4d03be15edc9f3a02d2e1b47a62b83e619c4b1f82e09856d91a8bfed3', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', '1123', 'RFGTRC', '*************', 523.980, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-01', '2024-11-10 13:12:49.628352+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'c9a84b5f5e6c2719b82f4c14d5f7b8d3205d7812e05c93b7d2c4e8a7312fa7e');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815785, 'a9e7c58e51b04d6ba34d7e92d1f3c2b90f6e4b789a81f7d650b3e29a5d6a7c9', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', '1123', 'RFTRGF', '*************', 679.430, 'VI', 'EL', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-02', '2024-11-10 13:12:50.628352+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '400006', '0000', 'US', NULL, '********', 'd2510f6e4b78c5f43e6a8c5a2d18b2f4e98f7e6b0c23b571f3c4d8b67a5c09e');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815780, 'b23a8c9f0e51c6d4a7b92f8e1d7c50a7e64f3b1e0c78f9a5d5c8b9a4f20e9c', NULL, NULL, 'EL201RFRC', '*********', '202404021212_SA', '1125', 'RFHYUT', '*************', 931.290, 'DI', 'DI', 'BSP', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-10', '2024-07-03', '2024-11-10 13:12:51.628352+00', 'AGENCY', 'AGENCY_BSP', 'AA00', '300006', '0000', 'US', NULL, '********', 'e18b7c9f5c2d0b6e4a9d81e0f47a2f5e6c3b7d8c4e09a2f5c67b4d2e1f0a8c3');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815894, 'bf7c80a1d03ae95edb6f2e0a4b85f278c19f354fe2e2f0d7c0b31e5a689f42', NULL, NULL, 'EL201RFRC', 'R20241126', NULL, NULL, 'RFJHBN', '*************', 456.540, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', false, '2024-11-26', '2023-11-11', '2024-11-26 06:40:06.504298+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'ae6d941c5e8f0718b25b4cy4cf755e9d3702c951yf50f28171160c8f37a2111');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815861, 'd32f58e70c6a9d8f15b42c3e7f1d4b9826e71b604f9f32a0d23f68c7153bdc9', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1423', 'RFYUSR', '*************', 68.570, 'TP', 'TP', 'ARC', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-05', '2024-11-11 18:25:54.117234+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100106', '0000', 'BE', NULL, '********', 'y104f3c635ce8a17y845eeb97d19f162674cc53894172cdfe5b0c025f5b0daf');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815836, 'f7a8c2e3d5b9f1d0b4a6e9c7d3f0a5b2c9f4b1e7d8a3c6e1f5d2b7a0c4e9b8', NULL, '6G4LPX1TQ8V7N5Y3', 'EL201RFRC', '*********', '202404021213_SA', '1146', 'RFQJXZ', '*************', 674.450, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-05', '2024-11-11 06:33:42.466748+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '6a4b3e9f0c8d2a5b7c6e1f9a7b4d5f3c9e0a2d8f1b3e6c7f5a9b1d4c3e2f0');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815837, 'b6f8a7c3d4e5b0c2f9a1d7e6b5f3d9a4c0e2b7d1f6a8c9b4e0a3f2d5e7c1b9', NULL, '4M1LPQ8NXF3Y2V9D', 'EL201RFRC', '*********', '202404021213_SA', '1146', 'RFKDLP', '*************', 452.760, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-06', '2024-11-11 06:33:58.586367+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '8b3c2d9e5f7a6f0d1c9a4b1e0f8d5c7a2e6b3a7d4f1c8b9e0a6f5d2c3e9b4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815838, 'c7a2b9e5d4f0c1a6f3e8d5b2a4e9f1c0b3a7d6f9c5e1b8f3d2a9b4c6e7f1d0', NULL, '2K9LFN4XT7P3Q6G5', 'EL201RFRC', '*********', '202404021214_SA', '1143', 'RFLJQW', '*************', 589.900, 'VI', 'EL', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-07', '2024-11-11 06:34:15.020125+00', 'LEGACY', 'COMPANY', 'AA00', '400062', '0007', 'US', NULL, '********', '3a5f7c4d2e9b6f0a8d1b4e3a9c2f5d7c6b1f8e0a4d9b3c7e2f0a6d3b5e1c4');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815862, '2d87c250365c99e4157f2abf44034be9f15c176yc7ef1fb0y5d6ec0c53818da', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1423', 'RFDTHC', '*************', 432.450, 'TP', 'TP', 'ARC', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-06', '2024-11-11 18:26:18.57913+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100106', '0000', 'BE', NULL, '********', '5361597faafy54104e0c7c0379b8ecbd4b67862015fyce5e1259f41dcd32c8f');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815863, '4a4y0cc9f35287f31bf1ecf219d7b6d205510ce768e1c87df4b5a39560y54ec', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1423', 'RFSRTD', '*************', 645.650, 'TP', 'TP', 'ARC', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-07', '2024-11-11 18:26:34.302685+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100106', '0000', 'BE', NULL, '********', 'aebdf840565e6fc25843647y8cff1d9b531a51911e0bec70fdc3225c94cy077');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815875, '4ee73fea3c292b8b91263066a1a32ca1a679b504306e0f34e8c7f5680aa267f2', NULL, NULL, 'EL203BBSP', 'B20241120', '202404021217_SA', '1408', 'A3J6KV', '*************', 3460.100, 'MC', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-10', '2024-11-20 16:25:25.907188+00', 'AGENCY', 'BSP_HOT', 'AA00', '235555', '7659', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815876, '5672ac160e1b0d0501cb575a81233b29a7e96630bf8c207f229fb13201437a9f', NULL, NULL, 'DI209BBSP', 'B20241120', '202404021217_SA', '1405', 'B4L9HW', '*************', 7461.110, 'DI', 'DI', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240119', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-11', '2024-11-20 16:25:26.150444+00', 'AGENCY', 'BSP_HOT', 'AA00', '309674', '8638', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815412, '1c9246d7b1d2c9855864e01a3fd36554eb35e624d387abcb82af9301d744a93b', '********-2fc2-4760-9968-319967233f26', 'ZK7Z45VQM33ZBMV5', '86de0aa22847eb93579fb0f6592e50c9fd7e5c331c202d15dfa28c1ad38ad4c2', 'B20240930', NULL, NULL, 'POFTYT', '*************', 765.010, 'AX', 'AD', 'COMPANY', 'SALE', 'USD', 'AA00', NULL, NULL, '1240019', '1240152', 'AX Co Sales Billing', false, '2024-09-30', '2024-07-02', '2024-09-30 13:51:37.332355+00', 'CPS', 'COMPANY', 'AA00', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815812, 'b3a9d4f5e8c1d7b6a2c0f1b9e6d3f4c7a1e5f2b8d0c9a3e7f6d2b5c4a8e1f0', NULL, 'YWP3KLV6NXF4T8R2', '9cf2119199c7ae1df52215a40237e71dcd7d619c283f33d39813967790a0d3da', '*********', NULL, NULL, 'RFDLKH', '*************', 611.230, 'TP', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240001', '1240071', 'TP Co Refund Billing', false, '2024-11-10', '2024-07-06', '2024-11-10 18:27:39.646075+00', 'CPS', 'COMPANY', 'AA00', '100167', '4565', 'US', 'STANDARD', '********', 'd4e5b2c8f7a9b3e6c1d9f0e4a7b6c3f1d8a5c2e9f3b0d7e6a1f4c5b8e0d3f9');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815810, 'f9b2e1c7a5d6f3b8c0a4b9e8f1d3c6e2b7a0d5c9e4f6a7b1d8c3f0e2a9b6d4', NULL, 'PRL9TXF3KYM7W4C5', '8f5b685318d05c5ee9cff5d9a9edf3a34641c5fb5732c9829a1eeb8502cf7db2', '*********', NULL, NULL, 'RFHDLC', '*************', 801.790, 'OA', 'TP', 'COMPANY', 'REFUND', 'EUR', 'AA00', ' ', NULL, '1240009', '1240071', 'OA TP Co Refund Billing', false, '2024-11-10', '2024-07-04', '2024-11-10 18:27:06.156403+00', 'CPS', 'COMPANY', 'AA00', '100267', '4565', 'US', 'STANDARD', '********', 'e5b8c1d3f6a9b4f2c7a1e0b6d2f3e4c9b5f1a7d8e6c0a3b9f4d7c5e9a2b6f8');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815844, 'a3d9f5c7b6e1a4c0d2b9e0f8b3a7d6e4f1c2a9d8b0f4c3a5e7d2f1b6c4a9', NULL, 'G5N3LXY8V7T2F6M4', 'TP211BARC', '*********', '202404021212_SA', 'UATP-1392', 'RFYGZT', '*************', 895.200, 'OA', 'TP', 'COMPANY', 'REFUND', 'USD', 'AA00', '', '***************', '1240001', '1240052', 'C2 Sales Billing', true, '2024-11-11', '2024-07-04', '2024-11-11 07:34:07.816615+00', 'LEGACY', 'COMPANY', 'AA00', '100254', '6789', 'US', NULL, '********', 'e0a4b7f2c9a5d8f1b6c3e9d4a1f7b0d2c8f5a3e6b9f0d7c1a3e4f8b2c5a7');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815859, 'c14f25d60e3b8d7a32f13e5c7b9d4c9835e72c607a3f92b1d46f58a9142deb7', NULL, NULL, 'EL201RFRC', '*********', '202404021211_SA', 'UATP-1424', 'RFKISF', '*************', 537.670, 'OA', 'TP', 'ARC', 'REFUND', 'USD', 'AA00', ' ', NULL, '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-03', '2024-11-11 18:25:17.907754+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '100406', '0000', 'US', NULL, '********', '775d324f8c5c855a0b64d75a2cf2ac3918y51f9b789e11cy5efe12e6c01400b');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815886, '212420bd09a7dc92734e5aac0d5315f791bbcc9d10af524fb94e3ca3b5d99d53', NULL, NULL, 'EL203BARC', 'B20241120', NULL, NULL, 'CTACNT', '*************', 100.000, 'MC', 'EL', 'ARC', 'SALE', 'USD', 'AA00', '', '**********', '1240103', '1240052', 'C2 Sales Billing', false, '2024-11-20', '2024-05-12', '2024-11-20 16:25:47.511261+00', 'AGENCY', 'ARC_CAT', 'AA00', '500069', '8465', 'US', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815848, 'bf7c80a1d03ae95edb6f2e0a4b85f278c19f354fe2e2f0d7c0b31e5a689f42', NULL, NULL, 'EL201RFRC', '*********', '202404021215_SA', '1243', 'RFJHBN', '*************', 456.540, 'VI', 'EL', 'ARC', 'REFUND', 'USD', 'AA00', ' ', '**********', '1240130', '1240152', 'C2 Refund Billing', true, '2024-11-11', '2024-07-10', '2024-11-11 18:23:49.68114+00', 'AGENCY', 'AGENCY_ARC', 'AA00', '400006', '0000', 'US', NULL, '********', 'ae6d941c5e8f0718b25b4cy4cf755e9d3702c951yf50f28171160c8f37a20dc');
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815872, '80e02e9ade829673d06d6dd919b09b4fd5a38a387f7503aed5ffe26d4619d70d', NULL, NULL, 'EL204BBSP', 'B20241120', '202404021217_SA', '1408', 'V2W6HP', '*************', 6457.070, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-07', '2024-11-20 16:25:25.315096+00', 'AGENCY', 'BSP_HOT', 'AA00', '455454', '9677', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815881, '9db4ddee5ab75320185689a02fed56b1803b2898360030619c00895e4dd353a5', NULL, NULL, 'TP206BBSP', 'B20241120', '202404021217_SA', 'UATP-1412', 'H7Q2BX', '*************', 7646.160, 'TP', 'TP', 'BSP', 'SALE', 'EUR', 'AA00', '', NULL, '1240009', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-16', '2024-11-20 16:25:27.166297+00', 'AGENCY', 'BSP_HOT', 'AA00', '100145', '6452', 'BE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815882, '48eae6935fa9cf7d3f23a036d1f6aa9643ca23d5de5d36f9c17b4b314919b02a', NULL, NULL, 'TP206BBSP', 'B20241120', '202404021217_SA', 'UATP-1412', 'N4K8JV', '*************', 77.170, 'TP', 'TP', 'BSP', 'SALE', 'EUR', 'AA00', '', NULL, '1240009', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-17', '2024-11-20 16:25:27.371738+00', 'AGENCY', 'BSP_HOT', 'AA00', '100187', '1553', 'BE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815883, '8320713d07dffb7b8b6c028fb9e338c76d15f47e62c2ee82ab0f0939c3b44e3e', NULL, NULL, 'TP206BBSP', 'B20241120', '202404021217_SA', 'UATP-1412', 'X2L5WP', '*************', 548.180, 'TP', 'TP', 'BSP', 'SALE', 'EUR', 'AA00', '', NULL, '1240009', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-18', '2024-11-20 16:25:27.486714+00', 'AGENCY', 'BSP_HOT', 'AA00', '100134', '9854', 'BE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815889, '5cf1984b3a2cebc77c2e1bbb1fd77de1ace73d28b9c4385820cc927ae5ddae15', NULL, NULL, 'EL204BBSP', 'B20241122', '202404021217_SA', '1409', 'Z4G7MX', '*************', 1452.020, 'VI', 'EL', 'BSP', 'SALE', 'AED', 'AA00', '', '**********', '1240130', '1240152', 'C2 Sales Billing', true, '2024-11-22', '2024-07-02', '2024-11-22 19:23:40.759445+00', 'AGENCY', 'BSP_HOT', 'AA00', '400153', '5463', 'AE', NULL, '********', NULL);
INSERT INTO rec_account.receivable_accounting_trans_details (detail_id, document_uuid, client_capture_received_id, psp_reference, reference_id, recon_id, post_to_ledger, aggregate_id, pnr, ticket_number, amount, payment_type, processor, sales_channel, transaction_type, currency, debit_company_code, cost_center, merchant_id, debit_account, credit_account, line_item_text, is_processed, transaction_date, issue_date, created_timestamp, payment_processing_path, sales_source, credit_company_code, card_bin, card_last_four_digits, country_code, fop_channel, station_number, refund_id) VALUES (1815873, '578798c18cb680c194da550805670c64a1f9b057fbbed43c8aa44a0930d95729', NULL, NULL, 'DS209BBSP', 'B20241120', '202404021217_SA', '1410', 'M7N4QX', '*************', 958.080, 'DI', 'DS', 'BSP', 'SALE', 'HNL', 'AA00', '', '***************', '1240121', '1240152', 'C2 Sales Billing', true, '2024-11-20', '2024-07-08', '2024-11-20 16:25:25.514941+00', 'AGENCY', 'BSP_HOT', 'AA00', '394543', '9087', 'HN', NULL, '********', NULL);
