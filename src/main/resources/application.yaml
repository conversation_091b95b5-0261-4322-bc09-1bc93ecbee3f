spring:
  cloud:
    azure:
      servicebus:
        namespace: ${CCRECON_SBQ_NAMESPACE}
        credential:
          client-id: ${CCRECON_SBQ_LISTEN_CLIENT_ID}
          client-secret: ${CCRECON_SBQ_LISTEN_CLIENT_SECRET}
        profile:
          tenant-id: ${AZURE_TENANT_ID}
    stream:
      bindings:
        consumeAggregatedGeneralLedgerTransactionDetailsMessage-in-0:
          destination: ${ACCOUNTING_GL_QUEUE_NAME}
          binder: servicebus
        consumeAggregatedSettlementMessage-in-0:
          destination: ${SETTLEMENT_GL_QUEUE_NAME}
          binder: servicebus
        supplyAggregatedGeneralLedgerTransactionDetailsMessage-out-0:
          destination: ${ACCOUNTING_GL_QUEUE_NAME}
          binder: servicebus
        sendExceptionMessage-out-0:
          destination: ${ACCOUNTING_EXCEPTION_QUEUE_NAME}
          binder: servicebus
      servicebus:
        bindings:
          consumeAggregatedGeneralLedgerTransactionDetailsMessage-in-0:
            consumer:
              auto-complete: false
          consumeAggregatedSettlementMessage-in-0:
            consumer:
              auto-complete: false
          supplyAggregatedGeneralLedgerTransactionDetailsMessage-out-0:
            producer:
              entity-type: queue
          sendExceptionMessage-out-0:
            producer:
              entity-type: queue
    function:
      definition: consumeAggregatedGeneralLedgerTransactionDetailsMessage;supplyAggregatedGeneralLedgerTransactionDetailsMessage;sendExceptionMessage;consumeAggregatedSettlementMessage;
              
  datasource:
    url: ${CCRECON_DB_URL}
    username: ${CCRECON_DB_USERNAME}
    password: ${CCRECON_DB_PASSWORD}
    driverClassName: org.postgresql.Driver
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
  profiles:
    active: ${REC_ACCT_GL_PROFILE:default}
  security:
    oauth2:
      client:
        registration:
          apigee:
            client-id: ${APIGEE_CLIENT_ID}
            client-secret: ${APIGEE_CLIENT_SECRET}
            authorization-grant-type: client_credentials
        provider:
          apigee:
            token-uri: ${APIGEE_TOKEN_URI}

sap:
  decimalFormat: ${SAP_DECIMAL_FORMAT:}
  documentHeaderText: ${SAP_DOCUMENT_HEADER_TEXT:}
  postingKeyCredit: ${SAP_POSTING_KEY_CREDIT:}
  postingKeyDebit: ${SAP_POSTING_KEY_DEBIT:}
  destination: ${SAP_DESTINATION:}
  source: ${SAP_SOURCE:}
  interface: ${SAP_INTERFACE:}
  domain: ${SAP_DOMAIN:}
  service: ${SAP_SERVICE:}
  transtype: ${SAP_TRANSTYPE:}
  userId: ${SAP_USERID:}
  execMode: ${SAP_EXECMODE:}
  procOption: ${SAP_PROCOPTION:}

  url: ${SAP_BASE_URL}
  connectTimeout: ${SAP_CONNECT_TIMEOUT}
  readTimeout: ${SAP_READ_TIMEOUT}

management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"

  health:
    defaults:
      enabled: false
    redis:
      enabled: false
    binders:
      enabled: false

    status:
      http-mapping:
        DOWN: 503
        
retry:
  delay: 1
  times: 2
        
ccrecon:
  service-bus-millisecond-delay: ${RECEIVABLE_ACCOUNTING_GL_SERVICE_BUS_MILLISECOND_DELAY}
  message-retry-count-limit: ${RECEIVABLE_ACCOUNTING_GL_SERVICE_BUS_RETRY_LIMIT}


