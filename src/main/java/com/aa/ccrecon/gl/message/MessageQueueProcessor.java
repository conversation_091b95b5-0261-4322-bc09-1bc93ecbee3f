package com.aa.ccrecon.gl.message;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.gl.config.AppConfig;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import com.aa.itfacs.pmt.mask.Masked;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.aa.ccrecon.gl.model.GlResponseEnum.GLPOSTING_FAILEDCONNECTION;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getCcReconHeader;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getGlobalId;
import static com.aa.ccrecon.gl.utils.ModelExtractor.setCcReconHeader;

@Component
@Slf4j
@RequiredArgsConstructor
public class MessageQueueProcessor {


    private final MessageQueuePublisher messageQueuePublisher;
    private final AppConfig appConfig;

    public void retryTransactionRequest(AggregatedTransaction aggregatedTransaction) {

        CcReconHeader ccReconHeader = getCcReconHeader.apply(aggregatedTransaction).orElse(new CcReconHeader());
        int scheduleMessageRetryCount = ccReconHeader.getMessageEventRescheduleCount();
        scheduleMessageRetryCount++;
        if (scheduleMessageRetryCount <= appConfig.getMessageRetryCountLimit()) {
            ccReconHeader.setMessageEventRescheduleCount(scheduleMessageRetryCount);
            setCcReconHeader.accept(aggregatedTransaction, ccReconHeader);
            messageQueuePublisher.sendToAccountingGlQueue(aggregatedTransaction);
        } else {
            log.warn("Exhausted '{}' retries for Global ID: {}", appConfig.getMessageRetryCountLimit(), getGlobalId.apply(aggregatedTransaction));
            List<CcReconException> ccReconExceptions = ModelExtractor.exceptions.apply(aggregatedTransaction);
            ccReconExceptions.get(0).setExceptionCode(GLPOSTING_FAILEDCONNECTION.name());
            ccReconHeader.setCcReconExceptions(ccReconExceptions);
            setCcReconHeader.accept(aggregatedTransaction, ccReconHeader);
            log.info("Sending message to exception queue: {}", Masked.objectToMaskedString(aggregatedTransaction));
            log.error("Retries exhausted, exception code: {}", GLPOSTING_FAILEDCONNECTION.name());
            messageQueuePublisher.sendToExceptionQueue(aggregatedTransaction);
        }
    }
}
