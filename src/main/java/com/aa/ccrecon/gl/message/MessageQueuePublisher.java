package com.aa.ccrecon.gl.message;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.gl.config.AppConfig;
import com.aa.ccrecon.gl.utils.AppConstants;
import com.aa.itfacs.pmt.mask.Masked;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;

@Component
@Slf4j
@RequiredArgsConstructor
public class MessageQueuePublisher {


    private final Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedGeneralLedgerTransactionDetailsSink;

    private final Sinks.Many<Message<AggregatedTransaction>> sendExceptionMessageSink;
    private final AppConfig appConfig;

    public void sendToAccountingGlQueue(AggregatedTransaction aggregatedTransaction) {
        log.debug("Going to add message {} to Sinks.Many.", Masked.objectToMaskedString(aggregatedTransaction));
        serviceBusAggregatedGeneralLedgerTransactionDetailsSink
                .emitNext(
                        MessageBuilder.withPayload(aggregatedTransaction)
                                .setHeader(AppConstants.MessageHeaderNames.SCHEDULED_MESSAGE_MILLISECOND_DELAY_NAME,
                                        appConfig.getServiceBusMillisecondDelay())
                                .build(),
                        Sinks.EmitFailureHandler.FAIL_FAST);
    }

    public void sendToExceptionQueue(AggregatedTransaction aggregatedTransaction) {
        log.debug("Sending exception message to exception queue: {}", Masked.objectToMaskedString(aggregatedTransaction));
        sendExceptionMessageSink.emitNext(MessageBuilder.withPayload(aggregatedTransaction).build(), Sinks.EmitFailureHandler.FAIL_FAST);
    }
}
