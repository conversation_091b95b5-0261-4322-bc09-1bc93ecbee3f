package com.aa.ccrecon.gl.client;

import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import static com.aa.ccrecon.gl.utils.ModelExtractor.getGlobalIdFromSapRequest;

@Component("DirectSapConnector")
@Slf4j
@Profile("!virtual-service")
public class DirectSapConnector implements SapConnector {

    private final WebClient webClient;

    public DirectSapConnector(@Qualifier("SAPWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    @Override
    public GlPostingResponse postToSap(GeneralLedgerEnvelope glEnvelope) throws SapResponseException {
        try {
            log.info("Sending request to SAP {}", glEnvelope);
            return webClient.post().uri("/postJournalEntry")
                    .accept(MediaType.APPLICATION_JSON)
                    .bodyValue(glEnvelope)
                    .retrieve()
                    .bodyToMono(GlPostingResponse.class)
                    .block();
        } catch (WebClientResponseException wcre) {
            String globalId = getGlobalIdFromSapRequest.apply(glEnvelope); // Replace with actual globalId once determined

            var statusCode = wcre.getStatusCode().value();
            var respBody = wcre.getResponseBodyAsString();

            log.error("Exception calling SAP, globalId: {}, statusCode: {}, response: {}", globalId, statusCode, respBody);
            throw new SapResponseException(respBody,
                    wcre.getMessage(),
                    statusCode);
        } catch (Exception re) {
            throw new RuntimeException("Unexpected error occurred", re);
        }
    }
}
