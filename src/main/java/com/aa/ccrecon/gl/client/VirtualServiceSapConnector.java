package com.aa.ccrecon.gl.client;

import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Profile("virtual-service")
@Primary
public class VirtualServiceSapConnector implements SapConnector {
    @Override
    public GlPostingResponse postToSap(GeneralLedgerEnvelope generalLedgerEnvelope) {
        log.info("GL message is not sent to SAP. Posting is disabled in this environment.");
        return null;
    }
}
