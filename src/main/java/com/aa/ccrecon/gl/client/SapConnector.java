package com.aa.ccrecon.gl.client;

import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;

public interface SapConnector {
    default GlPostingResponse postToSap(GeneralLedgerEnvelope generalLedgerEnvelope) throws SapResponseException {
        throw new UnsupportedOperationException("This method should be implemented by subclasses");
    }
}
