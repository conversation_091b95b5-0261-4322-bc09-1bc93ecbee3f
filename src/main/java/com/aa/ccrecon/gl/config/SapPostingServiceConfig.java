package com.aa.ccrecon.gl.config;

import com.aa.ccrecon.gl.exception.SapSvcNotImplemented;
import com.aa.ccrecon.gl.service.svclocator.SapSvcFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.config.ServiceLocatorFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration

public class SapPostingServiceConfig {

    /***
     * Configures a component lookup service based on the LookupFactory interface
     * @return
     */
    @Bean("lookupServiceFactory")
    public ServiceLocatorFactoryBean serviceLocatorFactoryBean() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        factoryBean.setServiceLocatorInterface(SapSvcFactory.class);
        factoryBean.setServiceLocatorExceptionClass(SapSvcNotImplemented.class);
        return factoryBean;
    }

}
