package com.aa.ccrecon.gl.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Data
@ConfigurationProperties(prefix = "sap")
@Configuration
public class SapRequestConfig {


    String decimalFormat;
    String documentHeaderText;
    String docType;
    String postingKeyCredit;
    String postingKeyDebit;
    String destination;
    String source;
    String interFace;
    String domain;
    String service;
    String transtype;
    String userId;
    String execMode;
    String procOption;
    String url;
    String cert;
    long connectTimeout;
    long readTimeout;


}



        

