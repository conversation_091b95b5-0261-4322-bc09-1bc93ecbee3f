package com.aa.ccrecon.gl.config;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.gl.service.MessageProcessorService;
import com.aa.itfacs.pmt.mask.Masked;
import com.azure.spring.messaging.checkpoint.Checkpointer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.azure.spring.messaging.AzureHeaders.CHECKPOINTER;

@Configuration
@Slf4j
public class ServiceBusConfig {


    @Bean
    public Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedGeneralLedgerTransactionDetailsSink() {
        return Sinks.many().unicast().onBackpressureBuffer();
    }

    @Bean
    public Sinks.Many<Message<AggregatedTransaction>> sendExceptionMessageSink() {
        return Sinks.many().unicast().onBackpressureBuffer();
    }

    @Bean
    Consumer<Message<AggregatedTransaction>> consumeAggregatedSettlementMessage(MessageProcessorService messageProcessorService) {
        return message-> {
            messageProcessorService.processSettlementAccounting(message.getPayload());

            Checkpointer checkpointer = (Checkpointer) message.getHeaders().get(CHECKPOINTER);
            if (checkpointer != null) {
                checkpointer.success()
                        .doOnSuccess(s -> log.info("Message '{}' successfully checkpointed", Masked.objectToMaskedString(message.getPayload())))
                        .doOnError(e -> log.error("Error found", e))
                        .block();
            }
        };
    }

	@Bean
    Consumer<Message<AggregatedTransaction>> consumeAggregatedGeneralLedgerTransactionDetailsMessage(MessageProcessorService messageProcessorService) {
        return message-> {
            messageProcessorService.processReceivableAccounting(message.getPayload());

            Checkpointer checkpointer = (Checkpointer) message.getHeaders().get(CHECKPOINTER);
            if (checkpointer != null) {
                checkpointer.success()
                        .doOnSuccess(s -> log.info("Message '{}' successfully checkpointed", Masked.objectToMaskedString(message.getPayload())))
                        .doOnError(e -> log.error("Error found", e))
                        .block();
            }
        };
    }

    @Bean
    public Supplier<Flux<Message<AggregatedTransaction>>> supplyAggregatedGeneralLedgerTransactionDetailsMessage(
            @Qualifier("serviceBusAggregatedGeneralLedgerTransactionDetailsSink") Sinks.Many<Message<AggregatedTransaction>> serviceBusAggregatedGeneralLedgerTransactionDetailsSink) {

        return () -> serviceBusAggregatedGeneralLedgerTransactionDetailsSink.asFlux()
                .doOnNext(m -> log.info("Manually sending message: {}", Masked.objectToMaskedString(m.getPayload())))
                .doOnError(t -> log.error("Error encountered", t));
    }

    @Bean
    public Supplier<Flux<Message<AggregatedTransaction>>> sendExceptionMessage(
            @Qualifier("sendExceptionMessageSink") Sinks.Many<Message<AggregatedTransaction>> sendExceptionMessageSink) {
        return () -> sendExceptionMessageSink.asFlux()
                .doOnNext(m -> log.info("Manually sending message: {}", Masked.objectToMaskedString(m.getPayload())))
                .doOnError(t -> log.error("Error encountered", t));
    }
}
