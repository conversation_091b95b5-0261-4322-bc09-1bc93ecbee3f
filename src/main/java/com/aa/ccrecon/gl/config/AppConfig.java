package com.aa.ccrecon.gl.config;

import jakarta.validation.constraints.Min;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.validation.annotation.Validated;

@Getter
@Validated
@ConfigurationProperties(prefix = "ccrecon")
public class AppConfig {

    @Min(0)
    private final Integer serviceBusMillisecondDelay;

    @Min(0)
    private final Integer messageRetryCountLimit;

    public AppConfig(@DefaultValue("0") Integer serviceBusMillisecondDelay,
                     @DefaultValue("3") Integer messageRetryCountLimit) {
        this.serviceBusMillisecondDelay = serviceBusMillisecondDelay;
        this.messageRetryCountLimit = messageRetryCountLimit;
    }



}
