package com.aa.ccrecon.gl.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

@Slf4j
public class HeadersLoggingFilter implements ExchangeFilterFunction {
    @Override
    public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
        request.headers().forEach((k, v) -> {;
            if (k.equalsIgnoreCase("Authorization") && v.getFirst().startsWith("Bearer ")) {
                log.info("Bearer token applied to request.");
            } else {
                log.info("{}: {}", k, String.join(", ", v));
            }
        });
        return next.exchange(request);
    }
}
