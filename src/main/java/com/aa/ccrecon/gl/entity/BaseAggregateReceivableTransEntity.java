package com.aa.ccrecon.gl.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.NotImplementedException;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Set;

@Getter
@Setter
@MappedSuperclass
public abstract class BaseAggregateReceivableTransEntity {

    @Column(name = "recon_id")
    protected String reconId;

    @Column(name = "post_to_ledger")
    protected String postToLedger;

    @Column(name = "currency")
    protected String currency;

    @Column(name = "credit_company_code")
    protected String creditCompanyCode;

    @Column(name = "debit_company_code")
    protected String debitCompanyCode;

    @Column(name = "debit_account")
    protected String debitAccount;

    @Column(name = "credit_account")
    protected String creditAccount;

    @Column(name = "line_item_text")
    protected String lineItemText;

    @Column(name = "is_processed")
    protected Boolean isProcessed;

    @Column(name = "global_id")
    protected String globalId;

    @Column(name = "batch_number")
    protected Integer batchNumber;

    @Column(name = "created_timestamp")
    protected LocalDateTime createdTimestamp;

    protected String convertedCurrency;

    public String getAggregateId() {
        throw new NotImplementedException();
    }

    public Set<Long> getDetailIds() {
        return Collections.emptySet();
    }

}
