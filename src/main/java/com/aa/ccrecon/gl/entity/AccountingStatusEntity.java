package com.aa.ccrecon.gl.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;
import org.hibernate.annotations.CreationTimestamp;

import com.aa.itfacs.pmt.mask.annotation.Mask;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "accounting_status", schema = "c2")
public class AccountingStatusEntity {
    
	@Id
    @Column(name = "accounting_status_uuid")
	private UUID accountingStatusUuid;
	
	@CreationTimestamp
	@Column(name = "change_timestamp")
    private LocalDateTime changeTimestamp;
	
	@Column(name = "issue_date")
    private LocalDate issueDate;
	
	@Column(name = "document_uuid")
    private String documentUuid;
	
	@Mask
	@Column(name = "pnr_number")
    private String pnrNumber;
	
	@Mask
	@Column(name = "ticket_number")
    private String ticketNumber;
	
	@Column(name = "currency_code")
    private String currencyCode;
	
	@Column(name = "amount_capture")
    private BigDecimal amountCapture;
	
	@Column(name = "client_capture_received_id")
    private UUID clientCaptureReceivedId;
	
	@Column(name = "psp_reference")
    private String pspReference;

	@Column(name = "comments")
	private String comments;
	
	@Column(name = "status")
    private String status;

    @Mask
    @Column(name = "card_bin")
    private String cardBin;

    @Mask
    @Column(name = "card_last_four_digits")
    private String cardLastFourDigits;

    @Column(name = "refund_id")
    private String refundId;

    @Column(name = "transaction_type")
    private String transactionType;

}
