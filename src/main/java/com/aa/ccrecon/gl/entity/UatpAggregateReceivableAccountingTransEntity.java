package com.aa.ccrecon.gl.entity;

import java.math.BigDecimal;
import java.util.*;

import jakarta.persistence.*;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Entity
@Table(name = "uatp_aggregate_receivable_accounting_trans", schema = "rec_account")
public class UatpAggregateReceivableAccountingTransEntity extends BaseAggregateReceivableTransEntity {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "aggregate_id")
	private String aggregateId;

	@Column(name = "sort_order")
	private Integer sortOrder;

	@Column(name = "amount")
	private BigDecimal amount;

	@Column(name = "record_type")
	private String recordType;

	@Column(name = "converted_amount")
	private BigDecimal convertedAmount;

	@OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "detail_id", referencedColumnName = "detail_id")
	private ReceivableAccountingTransDetailsEntity receivableAccountingTransDetailsEntity;

	@Override
	public Set<Long> getDetailIds() {
		return Optional.ofNullable(receivableAccountingTransDetailsEntity)
				.map(ReceivableAccountingTransDetailsEntity::getDetailId)
				.map(Set::of)
				.orElse(Collections.emptySet());
	}

}
