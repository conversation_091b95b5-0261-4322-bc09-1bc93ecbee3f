package com.aa.ccrecon.gl.entity;

import com.google.gson.Gson;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.sql.Timestamp;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "receivable_accounting_gl_posting" , schema = "rec_account")
public class ReceivableAccountingGLEntity extends ReceivableAccountingGLBase implements Comparable<ReceivableAccountingGLEntity> {

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "sap_summary_doc_id", length = 200, insertable = false, updatable = false)
    private String sapSummaryDocId;

    @Column(name = "sap_doc_number", length = 50)
    private String sapDocNumber;

    @Column(name = "global_id")
    private String globalId;

    @Column(name = "local_currency")
    private String localCurrency;

    @Column(name = "company_code")
    private String companyCode;

    @Column(name = "gl_post_year")
    private int glPostYear;

    @Column(name = "gl_posting_status")
    private String glPostingStatus;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "request")
    private String request;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "response")
    private String response;

    @Column(name = "created_timestamp")
    @EqualsAndHashCode.Exclude
    private Timestamp createdTimestamp;

    @Override
    public int compareTo(@NotNull ReceivableAccountingGLEntity o) {
        Gson g = new Gson();
        String first = g.toJson(this);
        String second = g.toJson(o);
        return first.compareTo(second);
    }
}
