package com.aa.ccrecon.gl.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "aggregate_receivable_accounting_trans", schema = "rec_account")
public class AggregateReceivableAccountingTransEntity extends BaseAggregateReceivableTransEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "aggregate_id")
	private String aggregateId;


	@Column(name = "sub_total")
	private BigDecimal subTotal;

	private BigDecimal convertedSubtotalAmount;

	@OneToMany(mappedBy="aggregateId", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	private Set<ReceivableAccountingTransDetailsEntity> receivableAccountingTransDetailsEntities;

}
