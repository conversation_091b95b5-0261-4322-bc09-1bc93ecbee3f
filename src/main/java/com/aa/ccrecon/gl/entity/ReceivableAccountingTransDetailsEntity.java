package com.aa.ccrecon.gl.entity;

import com.aa.itfacs.pmt.mask.annotation.Mask;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "receivable_accounting_trans_details", schema = "rec_account")
public class ReceivableAccountingTransDetailsEntity {
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "detail_id")
	private Long detailId;
	
	@Column(name = "document_uuid")
	private String documentUuid;
	
	@Column(name = "client_capture_received_id")
	private UUID clientCaptureReceivedId;
	
	@Column(name = "psp_reference")
	private String pspReference;
	
	@Column(name = "reference_id")
	private String referenceId;
	
	@Column(name = "recon_id")
	private String reconId;
	
	@Column(name = "post_to_ledger")
	private String postToLedger;
	
	@Column(name = "aggregate_id")
	private String aggregateId;
	
	@Mask
	@Column(name = "pnr")
	private String pnr;
	
	@Mask
	@Column(name = "ticket_number")
	private String ticketNumber;
	
	@Column(name = "amount")
	private BigDecimal amount;
	
	@Column(name = "payment_type")
	private String paymentType;
	
	@Column(name = "processor")
	private String processor;
	
	@Column(name = "sales_channel")
	private String salesChannel;
	
	@Column(name = "transaction_type")
	private String transactionType;
	
	@Column(name = "currency")
	private String currency;
	
	@Column(name = "credit_company_code")
	private String creditCompanyCode;

	@Column(name = "debit_company_code")
	private String debitCompanyCode;
	
	@Column(name = "cost_center")
	private String costCenter;
	
	@Column(name = "debit_account")
	private String debitAccount;
	
	@Column(name = "credit_account")
	private String creditAccount;
	
	@Column(name = "line_item_text")
	private String lineItemText;
	
	@Column(name = "is_processed")
	private boolean isProcessed;
	
	@Column(name = "transaction_date")
	private LocalDate transactionDate;
	
	@Column(name = "issue_date")
	private LocalDate issueDate;
		
	@CreationTimestamp
	@Column(name = "created_timestamp")
    private LocalDateTime createdTimestamp;

	@Mask
	@Column(name = "card_bin")
	private String cardBin;

	@Mask
	@Column(name = "card_last_four_digits")
	private String cardLastFourDigits;

	@Column(name = "refund_id")
	private String refundId;


}
