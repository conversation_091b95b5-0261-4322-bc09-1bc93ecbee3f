package com.aa.ccrecon.gl.entity;

import com.aa.ccrecon.gl.entity.compositeid.ReceivableAccountingGLSummaryEntityId;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@IdClass(ReceivableAccountingGLSummaryEntityId.class)
@Table(name = "receivable_accounting_gl_summary", schema = "rec_account")
public class ReceivableAccountingGLSummaryEntity extends ReceivableAccountingGLBase {


    @Column(name="sap_summary_doc_id", length = 200, insertable = false, updatable = false)
    String sapSummaryDocId;

    @Id
    @Column(name = "sap_doc_number")
    String sapDocNumber;

    @Id
    @Column(name = "local_currency")
    String localCurrency;

    @Id
    @Column(name = "company_code")
    String companyCode;

    @Id
    @Column(name = "gl_post_year")
    int glPostYear;

    @Column(name = "global_id")
    String globalId;

    @Column(name = "response_sub_total")
    BigDecimal responseSubTotal;

    @Column(name = "usd_converted_sub_total")
    BigDecimal usdConvertedSubTotal;

    private String transactionType;

}
