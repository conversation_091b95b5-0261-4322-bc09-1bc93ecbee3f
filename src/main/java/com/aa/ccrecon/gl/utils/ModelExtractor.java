package com.aa.ccrecon.gl.utils;

import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageEnvelope;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.aggregation.Payload;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.request.JournalEntryItem;
import com.aa.ccrecon.gl.model.sap.request.PostJournal;
import com.aa.ccrecon.gl.model.sap.request.RoutingInformation;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.model.sap.response.PostingDetails;
import org.apache.logging.log4j.util.Strings;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Function;

import static com.aa.ccrecon.gl.model.GlResponseEnum.GLPOSTING_DUPLICATE;
import static com.aa.ccrecon.gl.model.GlResponseEnum.GLPOSTING_ERROR;
import static com.aa.ccrecon.gl.utils.AppConstants.APP_NAME;

public class ModelExtractor {

    private ModelExtractor() {
    }

    public static final Function<AggregatedTransaction, Optional<Payload>> getPayload = aggregatedTransaction -> Optional.ofNullable(aggregatedTransaction)
            .map(AggregatedTransaction::getMessageEnvelope)
            .map(MessageEnvelope::getPayload);

    public static final Function<AggregatedTransaction, Optional<AggregatedGeneralLedgerTransactionDetails>> getAggregatedGeneralLedgerTransactionDetails = aggregatedTransaction -> getPayload.apply(aggregatedTransaction)
            .map(Payload::getAggregatedGeneralLedgerTransactionDetails);

    public static final Function<AggregatedTransaction, String> getMessageTypeName = aggregatedTransaction -> getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
            .map(AggregatedGeneralLedgerTransactionDetails::getMessageType)
            .map(MessageType::name)
            .orElse(null);

    public static final Function<AggregatedTransaction, Optional<CcReconHeader>> getCcReconHeader = aggregatedTransaction -> getPayload.apply(aggregatedTransaction)
            .map(Payload::getCcReconHeader);

    public static final BiConsumer<AggregatedTransaction, CcReconHeader> setCcReconHeader = (aggregatedTransaction, ccReconHeader) -> getPayload.apply(aggregatedTransaction)
            .ifPresent(payload -> payload.setCcReconHeader(ccReconHeader));

    public static final Function<AggregatedTransaction, String> getGlobalId = aggregatedTransaction -> getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
            .map(AggregatedGeneralLedgerTransactionDetails::getGlobalId)
            .orElse(null);

    public static final Function<AggregatedTransaction, List<CcReconException>> exceptions = aggregatedTransaction ->
    {
        List<CcReconException> exceptionList = Optional.ofNullable(aggregatedTransaction)
                .map(AggregatedTransaction::getMessageEnvelope)
                .map(MessageEnvelope::getPayload)
                .map(Payload::getCcReconHeader)
                .map(CcReconHeader::getCcReconExceptions)
                .orElseGet(ArrayList::new);
        exceptionList.add(createCcReconException(aggregatedTransaction));

        return exceptionList;
    };

    private static CcReconException createCcReconException(AggregatedTransaction aggregatedTransaction) {
        CcReconException ccReconException = new CcReconException();
        ccReconException.setExceptionUUID(UUID.randomUUID().toString());
        ccReconException.setCreatedDate(LocalDateTime.now());
        ccReconException.setExceptionSource(APP_NAME);
        ccReconException.setExceptionStatus("OPEN");
        ccReconException.setGlobalId(getGlobalId.apply(aggregatedTransaction));
        return ccReconException;
    }

    public static final Function<AggregatedTransaction, SalesSource> getSalesSource = aggregatedTransaction ->
            getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
                    .map(AggregatedGeneralLedgerTransactionDetails::getSalesSource)
                    .orElse(null);

    public static final Function<AggregatedTransaction, TransactionType.TYPES> getTransactionType = aggregatedTransaction ->
            getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
            .map(AggregatedGeneralLedgerTransactionDetails::getTransactionType)
            .orElse(null);

    public static final Function<AggregatedTransaction, AccountingType> getAccountingType = aggregatedTransaction ->
            getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
                    .map(AggregatedGeneralLedgerTransactionDetails::getAccountingType)
                    .orElse(null);

    public static final Function<GeneralLedgerEnvelope, Optional<List<JournalEntryItem>>> getJournalEntryDetails = generalLedgerEnvelope ->
            Optional.ofNullable(generalLedgerEnvelope)
                    .map(GeneralLedgerEnvelope::getPostJournal)
                    .map(PostJournal::getPostJournalDetail);

    public static final BiConsumer<GeneralLedgerEnvelope, List<JournalEntryItem>> addJournalEntryItems = (generalLedgerEnvelope, journalEntryItems) ->
            getJournalEntryDetails.apply(generalLedgerEnvelope)
                    .ifPresentOrElse(jes -> jes.addAll(journalEntryItems),
                            () -> generalLedgerEnvelope.getPostJournal().setPostJournalDetail(new ArrayList<>(journalEntryItems)));

    public static final Function<GeneralLedgerEnvelope, String> getGlobalIdFromSapRequest = glEnv ->
            Optional.ofNullable(glEnv)
                    .map(GeneralLedgerEnvelope::getPostJournal)
                    .map(PostJournal::getRoutingInfo)
                    .map(RoutingInformation::getGlobalId)
                    .orElse(Strings.EMPTY);

    public static final Function<GlPostingResponse, Optional<List<PostingDetails>>> getPostingDetails = glPostingResponse ->
            Optional.ofNullable(glPostingResponse)
                    .map(GlPostingResponse::getPostingDetails);

    public static final Function<GlPostingResponse, String> getErrorStatus = response ->
            Optional.ofNullable(response)
                    .map(GlPostingResponse::getPostingStatus)
                    .map(_ -> response.getErrorMessages().stream()
                            .filter(errMsg -> errMsg.getErrorLog() != null && errMsg.getErrorLog().contains("Duplicate"))
                            .findFirst()
                            .map(_ -> GLPOSTING_DUPLICATE.name())
                            .orElse(GLPOSTING_ERROR.name()))
                    .orElse(Strings.EMPTY);
}
