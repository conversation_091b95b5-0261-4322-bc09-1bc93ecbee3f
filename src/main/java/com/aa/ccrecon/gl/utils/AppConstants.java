package com.aa.ccrecon.gl.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AppConstants {
    public static final String APP_NAME = "itfacs-pmt-ccrecon-receivable-accounting-gl-service";

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class MessageHeaderNames {
        public static final String SCHEDULED_MESSAGE_MILLISECOND_DELAY_NAME = "x-delay";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class RecordType {
        public static final String AGGREGATE = "AGGREGATE";
        public static final String DETAIL = "DETAIL";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class DocHeaderText {
        public static final String REFUND_TEXT = "C2 Refunds";
        public static final String SALE_TEXT = "C2 Sales Billing";
    }
}
