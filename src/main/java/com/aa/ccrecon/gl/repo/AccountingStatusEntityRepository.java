package com.aa.ccrecon.gl.repo;

import com.aa.ccrecon.gl.entity.AccountingStatusEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.UUID;

public interface AccountingStatusEntityRepository extends JpaRepository<AccountingStatusEntity, UUID>{
    @Modifying
    @Query(value = """
            INSERT into c2.accounting_status as acctStatus (accounting_status_uuid, document_uuid, client_capture_received_id, psp_reference, pnr_number, ticket_number, currency_code, amount_capture, status, card_bin, card_last_four_digits, refund_id, transaction_type, issue_date)
                            select gen_random_uuid(), document_uuid, client_capture_received_id, psp_reference, pnr, ticket_number, deets.currency, amount, :status, card_bin, card_last_four_digits, refund_id, deets.transaction_type, issue_date
                                        from rec_account.receivable_accounting_trans_details deets
                                                    inner join rec_account.aggregate_receivable_accounting_trans agg on deets.aggregate_id = agg.aggregate_id
                                                    where agg.global_id = :globalId
            """, nativeQuery = true)
    void insertAccountingStatusNonUatp(String status, String globalId);

    @Modifying
    @Query(value = """
            INSERT into c2.accounting_status as acctStatus (accounting_status_uuid, document_uuid, client_capture_received_id, psp_reference, pnr_number, ticket_number, currency_code, amount_capture, status, card_bin, card_last_four_digits, refund_id, transaction_type, issue_date)
                            select gen_random_uuid(), document_uuid, client_capture_received_id, psp_reference, pnr, ticket_number, deets.currency, deets.amount, :status, card_bin, card_last_four_digits, refund_id, deets.transaction_type, issue_date
                                        from rec_account.receivable_accounting_trans_details deets
                                                    inner join rec_account.uatp_aggregate_receivable_accounting_trans agg on deets.detail_id = agg.detail_id
                                                    where agg.global_id = :globalId
            """, nativeQuery = true)
    void insertAccountingStatusUatp(String status, String globalId);
}
