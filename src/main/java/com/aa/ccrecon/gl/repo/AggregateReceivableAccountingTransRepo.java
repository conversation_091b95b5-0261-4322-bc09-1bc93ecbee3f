package com.aa.ccrecon.gl.repo;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AggregateReceivableAccountingTransRepo extends JpaRepository<AggregateReceivableAccountingTransEntity, String>  {

    List<AggregateReceivableAccountingTransEntity> findByGlobalId(String globalId);

    List<AggregateReceivableAccountingTransEntity> findByGlobalIdOrderByCurrencyAsc(String globalId);
}
