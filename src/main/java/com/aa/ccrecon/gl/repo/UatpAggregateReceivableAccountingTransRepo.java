package com.aa.ccrecon.gl.repo;

import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

public interface UatpAggregateReceivableAccountingTransRepo extends JpaRepository<UatpAggregateReceivableAccountingTransEntity, Integer> {

    List<UatpAggregateReceivableAccountingTransEntity> findByGlobalId(String globalId);

    List<UatpAggregateReceivableAccountingTransEntity> findByGlobalIdOrderByCurrencyAsc(String globalId);
}
