package com.aa.ccrecon.gl.service.documentheader;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.subledger.TransactionType;
import org.springframework.stereotype.Component;

import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.REFUND_TEXT;
import static com.aa.ccrecon.gl.utils.AppConstants.DocHeaderText.SALE_TEXT;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getTransactionType;

@Component
public class DocumentHeaderTextLocator implements DocHeaderService{
    @Override
    public String getDocHeaderText(AggregatedTransaction aggregatedTransaction) {
        //TODO: Implement logic to get Document Header Text from Redis. Should be combined with
        // DocTypeService changes to get all of these values from Redis
        TransactionType.TYPES type = getTransactionType.apply(aggregatedTransaction);
        return switch (type) {
            case SALE -> SALE_TEXT;
            case REFUND -> REFUND_TEXT;
            default -> throw new IllegalStateException("Unexpected transaction type: " + type);
        };
    }
}
