package com.aa.ccrecon.gl.service.aggregategl;

import java.sql.SQLException;
import java.util.List;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;

import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;

public interface LookupService {

    @Retryable(retryFor = SQLException.class, maxAttempts = 2, backoff = @Backoff(delay = 100))
    public List<AggregateReceivableAccountingTransEntity> findAggregateReceivableAccountingTrans(AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto);

    @Retryable(retryFor = SQLException.class, maxAttempts = 2, backoff = @Backoff(delay = 100))
    public List<UatpAggregateReceivableAccountingTransEntity> findUatpAggregateReceivableAccountingTransEntity(AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto);

    @Recover
    public void recover(SQLException e, String sql); 

}
