package com.aa.ccrecon.gl.service.aggregategl;

import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.gl.entity.BaseAggregateReceivableTransEntity;
import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.repo.UatpAggregateReceivableAccountingTransRepo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class AggregateReceivableAccountingTransFactory {

    private final UatpAggregateReceivableAccountingTransRepo uatpAggregateReceivableAccountingTransRepo;
    private final AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo;

    public List<? extends BaseAggregateReceivableTransEntity> findByGlobalId(String globalId, MessageType processType) {
        return switch (processType) {
            case AGGREGATED_GL_UATP -> uatpAggregateReceivableAccountingTransRepo.findByGlobalId(globalId);
            case AGGREGATED_GL_NON_UATP -> aggregateReceivableAccountingTransRepo.findByGlobalId(globalId);
            default -> null;
        };
    }

}
