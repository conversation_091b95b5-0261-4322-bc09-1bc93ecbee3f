package com.aa.ccrecon.gl.service.doctype;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.subledger.TransactionType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.aa.ccrecon.gl.utils.ModelExtractor.getTransactionType;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getSalesSource;

@Component
@RequiredArgsConstructor
public class DocTypeLocator implements DocTypeService {

    @Override
    public DocType getDocType(AggregatedTransaction aggregatedTransaction) {
        //TODO: Implement logic to get DocType from Redis
        TransactionType.TYPES type = getTransactionType.apply(aggregatedTransaction);
        return switch (type) {
            case SALE -> {
                if (getSalesSource.apply(aggregatedTransaction).equals(SalesSource.COMPANY_LOCAL)) {
                    // yes, I know this looks silly, BUT we'll need to update this value once SAP is ready to accept a
                    // different DocType for Local Billing transactions
                    yield DocType.E9;
                } else {
                    yield DocType.E9;
                }
            }
            case REFUND -> DocType.R9;
            default -> throw new IllegalStateException("Unexpected transaction type: " + type);
        };
    }
}
