package com.aa.ccrecon.gl.service.aggregategl;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.repo.AggregateReceivableAccountingTransRepo;
import com.aa.ccrecon.gl.repo.UatpAggregateReceivableAccountingTransRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

@Component
@Slf4j
public class LookupServiceImpl implements LookupService {

    private final AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo;
    private final UatpAggregateReceivableAccountingTransRepo uatpAggregateReceivableAccountingTransRepo;


    public LookupServiceImpl(AggregateReceivableAccountingTransRepo aggregateReceivableAccountingTransRepo, UatpAggregateReceivableAccountingTransRepo uatpAggregateReceivableAccountingTransRepo)
    {
        this.aggregateReceivableAccountingTransRepo = aggregateReceivableAccountingTransRepo;
        this.uatpAggregateReceivableAccountingTransRepo = uatpAggregateReceivableAccountingTransRepo;
    }

    public List<AggregateReceivableAccountingTransEntity> findAggregateReceivableAccountingTrans(AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails) {
        try {
           return aggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc(aggregatedGeneralLedgerTransactionDetails.getGlobalId());
        } catch (Exception e) {
            log.error("Exception encountered", e);
            return null;
        }
    }

    @Override
    public void recover(SQLException e, String sql) {
        log.info("Retry exhausted with following exception: {}",e.getMessage());
    }


    /***
     *
     * @param aggregatedGeneralLedgerTransactionDetails
     * @return
     */
    @Override
    public List<UatpAggregateReceivableAccountingTransEntity> findUatpAggregateReceivableAccountingTransEntity(
            AggregatedGeneralLedgerTransactionDetails aggregatedGeneralLedgerTransactionDetails) {
                try {
                    return uatpAggregateReceivableAccountingTransRepo.findByGlobalIdOrderByCurrencyAsc(
                            String.valueOf(aggregatedGeneralLedgerTransactionDetails.getGlobalId())
                    );
                 } catch (Exception e) {
                     return null;
                 }
    }


}
