package com.aa.ccrecon.gl.service.aggregategl;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.gl.client.SapConnector;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.mapper.SapRequestMapper;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.model.sap.response.PostingStatus;
import com.aa.ccrecon.gl.service.ReturnStatusService;
import com.aa.ccrecon.gl.service.svclocator.SapPostingInterface;
import com.aa.ccrecon.gl.validation.ValidationService;
import com.aa.itfacs.pmt.mask.Masked;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.aa.ccrecon.gl.utils.ModelExtractor.getAggregatedGeneralLedgerTransactionDetails;

@Service("AGGREGATED_GL_UATP")
@RequiredArgsConstructor
@Slf4j
public class UatpAggregatedTransactionProcessService implements SapPostingInterface {

    private final LookupServiceImpl lookupService;
    private final SapRequestMapper sapRequestMapper;
    private final SapRequestConfig sapRequestConfig;
    private final SapConnector sapConnector;
    private final ReturnStatusService returnStatusService;
    private final ValidationService validationService;

    @Override
    public void process(AggregatedTransaction aggregatedTransaction, DocType docType, final String docHeaderText) {
        getAggregatedGeneralLedgerTransactionDetails.apply(aggregatedTransaction)
                .ifPresentOrElse(aggregatedGeneralLedgerTransactionDetails -> {
                    log.info("uatpAggregateReceivableAccountingTransEntity: {}", Masked.objectToMaskedString(aggregatedGeneralLedgerTransactionDetails));
                    List<UatpAggregateReceivableAccountingTransEntity> uatpEntities =
                            lookupService.findUatpAggregateReceivableAccountingTransEntity(aggregatedGeneralLedgerTransactionDetails);

                    if (uatpEntities != null && !uatpEntities.isEmpty()) {
                        log.info("Number of records found: {}", uatpEntities.size());
                        sapRequestConfig.setDocType(docType.name());
                        sapRequestConfig.setDocumentHeaderText(docHeaderText);
                        GeneralLedgerEnvelope glEnvelope = sapRequestMapper.mapGlEnvelopeUatp(uatpEntities, aggregatedGeneralLedgerTransactionDetails, sapRequestConfig);
                        log.info("New request for direct SAP post: {}", Masked.objectToMaskedString(glEnvelope));

//                validationService.printValidationErrors(sapRequest);

                        try {
                            GlPostingResponse resp = sapConnector.postToSap(glEnvelope);
                            log.info("SAP post response: {}", Masked.objectToMaskedString(resp));
                            if (resp.getPostingStatus().equals(PostingStatus.Error)) {
                                log.error("SAP error response: {}", Masked.objectToMaskedString(resp));
                                returnStatusService.storeErrorResults(aggregatedTransaction, aggregatedGeneralLedgerTransactionDetails,
                                        glEnvelope, resp, MessageType.AGGREGATED_GL_UATP);
                            } else {
                                returnStatusService.storeResults(aggregatedGeneralLedgerTransactionDetails, glEnvelope,
                                        resp, MessageType.AGGREGATED_GL_UATP);
                            }
                        } catch (SapResponseException sRE) {
                            log.warn("SAP response exception", sRE);
                            returnStatusService.storeErrorAndRetry(aggregatedTransaction, aggregatedGeneralLedgerTransactionDetails,
                                    glEnvelope, sRE, MessageType.AGGREGATED_GL_UATP);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }

                    } else {
                        log.info("No record found..");
                    }
                }, () -> log.warn("No aggregatedGeneralLedgerTransactionDetails found.."));
    }
}
