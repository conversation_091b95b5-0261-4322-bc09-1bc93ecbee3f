package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.gl.service.doctype.DocTypeService;
import com.aa.ccrecon.gl.service.documentheader.DocHeaderService;
import com.aa.ccrecon.gl.service.svclocator.SapPostingInterface;
import com.aa.ccrecon.gl.service.svclocator.SapSvcFactory;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class MessageProcessorService {

    private final SapSvcFactory sapSvcFactory;
    private final DocTypeService docTypeService;
    private final DocHeaderService docHeaderService;


    public void processReceivableAccounting(AggregatedTransaction aggregatedTransaction) {
        final DocType docType = docTypeService.getDocType(aggregatedTransaction);
        final String docHeaderText = docHeaderService.getDocHeaderText(aggregatedTransaction);
        String messageType = ModelExtractor.getMessageTypeName.apply(aggregatedTransaction);
        SapPostingInterface sapService = sapSvcFactory.getPostingInterface(messageType);
        sapService.process(aggregatedTransaction, docType, docHeaderText);
    }

    public void processSettlementAccounting(AggregatedTransaction aggregatedTransaction) {
        log.info("Processing settlement accounting message: {}", aggregatedTransaction);
    }


}
