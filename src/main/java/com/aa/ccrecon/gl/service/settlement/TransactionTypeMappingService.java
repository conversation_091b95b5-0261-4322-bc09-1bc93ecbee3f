package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import org.springframework.stereotype.Service;



@Service
public class TransactionTypeMappingService {

    public TransactionTypeMapping getTransactionTypeMapping(TransactionType transactionType) {
        return switch (transactionType) {
            case SALE -> new TransactionTypeMapping(AccountType.CREDIT, "50");
            case REFUND, CHARGEBACK, FEE, BANKWIRE, DEPOSIT_CORRECT, INVOICE_DEDUCTION,
                 BALANCE_TRANSFER, RESERVE_ADJUSTMENT, MISC, ADJUSTMENT, UNKNOWN, REJECT ->
                new TransactionTypeMapping(AccountType.DEBIT, "40");
        };
    }

}
