package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import org.springframework.stereotype.Service;

/**
 * Service responsible for providing transaction type to debit/credit and post key mappings
 * for settlement accounting. This serves as a data dictionary for GL posting requirements.
 */
@Service
public class TransactionTypeMappingService {

    public TransactionTypeMapping getTransactionTypeMapping(TransactionType transactionType) {
        return switch (transactionType) {
            case SALE -> new TransactionTypeMapping(AccountType.CREDIT, "50");
            case REFUND, CHARGE<PERSON>CK, FEE, <PERSON><PERSON><PERSON>WIRE, DEPOSIT_CORRECT, INVOICE_DEDUCTION,
                 BALANCE_TRANSFER, RESERVE_ADJUSTMENT, MISC, ADJUSTMENT, UNKNOWN, REJECT ->
                new TransactionTypeMapping(AccountType.DEBIT, "40");
        };
    }

}
