package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service responsible for mapping transaction types to their corresponding
 * debit/credit designation and post keys for settlement accounting.
 */
@Service
@Slf4j
public class TransactionTypeMappingService {

    public void logTransactionTypeMapping(AggregatedTransaction aggregatedTransaction) {
        // Note: ModelExtractor returns subledger.TransactionType.TYPES, but we need to work with it
        // since that's what the domain model provides
        Object transactionTypeObj = ModelExtractor.getTransactionType.apply(aggregatedTransaction);
        String globalId = ModelExtractor.getGlobalId.apply(aggregatedTransaction);

        if (transactionTypeObj == null) {
            log.warn("Transaction type is null for Global ID: {}", globalId);
            return;
        }

        // Convert the transaction type to settlement TransactionType by name
        TransactionType settlementTransactionType = convertToSettlementTransactionType(transactionTypeObj);
        if (settlementTransactionType == null) {
            log.warn("Unable to convert transaction type {} for Global ID: {}", transactionTypeObj, globalId);
            return;
        }

        TransactionTypeMapping mapping = getTransactionTypeMapping(settlementTransactionType);

        log.info("Transaction Type Mapping - Global ID: {}, Transaction Type: {}, Debit/Credit: {}, Post Key: {}",
                globalId, settlementTransactionType, mapping.getDebitCredit(), mapping.getPostKey());
    }

    /**
     * Converts subledger TransactionType to settlement TransactionType by name matching.
     */
    private TransactionType convertToSettlementTransactionType(Object subledgerTransactionType) {
        try {
            String typeName = subledgerTransactionType.toString();
            return TransactionType.valueOf(typeName);
        } catch (IllegalArgumentException e) {
            log.warn("Unknown transaction type: {}", subledgerTransactionType);
            return null;
        }
    }

    /**
     * Gets the transaction type mapping based on the settlement transaction type.
     * Maps each transaction type to Debit/Credit & Post Key as per business requirements.
     *
     * @param transactionType the settlement transaction type
     * @return the mapping containing debit/credit designation and post key
     */
    public TransactionTypeMapping getTransactionTypeMapping(TransactionType transactionType) {
        return switch (transactionType) {
            case SALE -> new TransactionTypeMapping(AccountType.CREDIT, "50");
            case REFUND -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case CHARGEBACK -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case FEE -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case BANKWIRE -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case DEPOSIT_CORRECT -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case INVOICE_DEDUCTION -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case BALANCE_TRANSFER -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case RESERVE_ADJUSTMENT -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case MISC -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case ADJUSTMENT -> new TransactionTypeMapping(AccountType.DEBIT, "40");
            case REJECT -> new TransactionTypeMapping(AccountType.DEBIT, "40");
        };
    }

}
