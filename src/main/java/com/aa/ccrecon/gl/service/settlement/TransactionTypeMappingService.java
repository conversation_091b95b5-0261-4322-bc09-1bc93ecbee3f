package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.settlement.TransactionTypeMapping;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TransactionTypeMappingService {

    public void logTransactionTypeMapping(AggregatedTransaction aggregatedTransaction) {
        TransactionType transactionType = ModelExtractor.getSettlementTransactionType.apply(aggregatedTransaction);
        String globalId = ModelExtractor.getGlobalId.apply(aggregatedTransaction);

        if (transactionType == null) {
            log.warn("Transaction type is null for Global ID: {}", globalId);
            return;
        }

        TransactionTypeMapping mapping = getTransactionTypeMapping(transactionType);

        log.info("Transaction Type Mapping - Global ID: {}, Transaction Type: {}, Debit/Credit: {}, Post Key: {}",
                globalId, transactionType, mapping.getDebitCredit(), mapping.getPostKey());
    }

    public TransactionTypeMapping getTransactionTypeMapping(TransactionType transactionType) {
        return switch (transactionType) {
            case SALE -> new TransactionTypeMapping(AccountType.CREDIT, "50");
            case REFUND, CHARGEBACK, FEE, BANKWIRE, DEPOSIT_CORRECT, INVOICE_DEDUCTION,
                 BALANCE_TRANSFER, RESERVE_ADJUSTMENT, MISC, ADJUSTMENT, REJECT ->
                new TransactionTypeMapping(AccountType.DEBIT, "40");
            case UNKNOWN -> throw new IllegalStateException("Unexpected transaction type: " + transactionType);
        };
    }

}
