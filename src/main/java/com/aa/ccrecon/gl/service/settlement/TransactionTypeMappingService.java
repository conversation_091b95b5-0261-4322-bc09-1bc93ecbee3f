package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.model.AccountType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Service responsible for providing transaction type to debit/credit and post key mappings
 * for settlement accounting. This serves as a data dictionary for GL posting requirements.
 */
@Service
@RequiredArgsConstructor
public class TransactionTypeMappingService {

    private final SapRequestConfig sapRequestConfig;

    public AccountType getAccountType(TransactionType transactionType) {
        return switch (transactionType) {
            case SALE -> AccountType.CREDIT;
            case REFUND, CHARGEBACK, FEE, BANKWIRE, DEPOSIT_CORRECT, INVOICE_DEDUCTION,
                 BALANCE_TRANSFER, RESERVE_ADJUSTMENT, MISC, ADJUSTMENT, UNKNOWN, REJECT -> AccountType.DEBIT;
        };
    }

    public String getPostKey(AccountType accountType) {
        return accountType == AccountType.DEBIT ? sapRequestConfig.getPostingKeyDebit() : sapRequestConfig.getPostingKeyCredit();
    }

}
