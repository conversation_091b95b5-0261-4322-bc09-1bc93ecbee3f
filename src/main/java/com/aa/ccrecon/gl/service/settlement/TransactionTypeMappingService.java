package com.aa.ccrecon.gl.service.settlement;

import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.settlement.TransactionType;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.utils.ModelExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service responsible for mapping transaction types to their corresponding
 * debit/credit designation and post keys for settlement accounting.
 */
@Service
@Slf4j
public class TransactionTypeMappingService {

    public void logTransactionTypeMapping(AggregatedTransaction aggregatedTransaction) {
        TransactionType.TYPES transactionType = ModelExtractor.getTransactionType.apply(aggregatedTransaction);
        String globalId = ModelExtractor.getGlobalId.apply(aggregatedTransaction);
        
        if (transactionType == null) {
            log.warn("Transaction type is null for Global ID: {}", globalId);
            return;
        }

        TransactionTypeMapping mapping = getTransactionTypeMapping(transactionType);
        
        log.info("Transaction Type Mapping - Global ID: {}, Transaction Type: {}, Debit/Credit: {}, Post Key: {}", 
                globalId, transactionType, mapping.getDebitCredit(), mapping.getPostKey());
    }

    public TransactionTypeMapping getTransactionTypeMapping(TransactionType transactionType) {
        // SALE maps to CREDIT with Post Key 50
        if (TransactionType.TYPES.SALE.equals(transactionType)) {
            return new TransactionTypeMapping(AccountType.CREDIT, "50");
        }
        return new TransactionTypeMapping(AccountType.DEBIT, "40");
    }

    /**
     * Data class representing the mapping of a transaction type to its
     * debit/credit designation and post key.
     */
    public static class TransactionTypeMapping {
        private final AccountType debitCredit;
        private final String postKey;

        public TransactionTypeMapping(AccountType debitCredit, String postKey) {
            this.debitCredit = debitCredit;
            this.postKey = postKey;
        }

        public AccountType getDebitCredit() {
            return debitCredit;
        }

        public String getPostKey() {
            return postKey;
        }

        @Override
        public String toString() {
            return String.format("TransactionTypeMapping{debitCredit=%s, postKey='%s'}", debitCredit, postKey);
        }
    }
}
