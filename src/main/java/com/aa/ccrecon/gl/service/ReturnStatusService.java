package com.aa.ccrecon.gl.service;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.BaseAggregateReceivableTransEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLSummaryEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.exception.SapResponseExceptionDto;
import com.aa.ccrecon.gl.mapper.RecAccGLPostingMapper;
import com.aa.ccrecon.gl.mapper.RecAccGlSummaryMapper;
import com.aa.ccrecon.gl.message.MessageQueueProcessor;
import com.aa.ccrecon.gl.model.GlResponseEnum;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.repo.AccountingStatusEntityRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLPostingRepository;
import com.aa.ccrecon.gl.repo.ReceivableAccountingGLSummaryRepository;
import com.aa.ccrecon.gl.service.aggregategl.AggregateReceivableAccountingTransFactory;
import com.aa.ccrecon.gl.utils.AppConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.aa.ccrecon.gl.utils.ModelExtractor.getErrorStatus;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getPostingDetails;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getTransactionType;

@Service
@RequiredArgsConstructor
public class ReturnStatusService {

    private final Logger logger = LoggerFactory.getLogger(ReturnStatusService.class);

    private final RecAccGLPostingMapper recAccGLPostingMapper;
    private final ReceivableAccountingGLPostingRepository receivableAccountingGLPostingRepository;
    private final ReceivableAccountingGLSummaryRepository receivableAccountingGLSummaryRepository;
    private final AggregateReceivableAccountingTransFactory aggregateReceivableAccountingTransFactory;
    private final AccountingStatusEntityRepository accountingStatusEntityRepository;
    private final RetryService retryService;
    private final MessageQueueProcessor messageQueueProcessor;
    private final RecAccGlSummaryMapper summaryMapper;

    @Transactional
    public void storeErrorResults(AggregatedTransaction aggregatedTransaction, AggregatedGeneralLedgerTransactionDetails aggTransDetails, GeneralLedgerEnvelope sapRequest,
                                  GlPostingResponse sapResponse, MessageType messageType) {
        try {
            List<? extends BaseAggregateReceivableTransEntity> aggregateReceivableAccountingTransEntityList =
                    aggregateReceivableAccountingTransFactory.findByGlobalId(aggTransDetails.getGlobalId(), messageType);
            List<? extends BaseAggregateReceivableTransEntity> filteredAggregateReceivableAccountingTransEntityList = aggregateReceivableAccountingTransEntityList.stream()
                    .filter(entity -> entity instanceof AggregateReceivableAccountingTransEntity || AppConstants.RecordType.AGGREGATE.equals(((UatpAggregateReceivableAccountingTransEntity) entity).getRecordType()))
                    .toList();

            logger.info("Number of aggregate records found: {}", filteredAggregateReceivableAccountingTransEntityList.size());

            for (BaseAggregateReceivableTransEntity transEntity : filteredAggregateReceivableAccountingTransEntityList) {
                ReceivableAccountingGLEntity receivableAccountingGLEntity = recAccGLPostingMapper
                        .mapErrorResponse(sapResponse, sapRequest, aggTransDetails);
                TransactionType.TYPES transactionType = getTransactionType.apply(aggregatedTransaction);
                receivableAccountingGLEntity.setTransactionType(transactionType != null ? transactionType.toString() : null);
                saveReceivableAccountingGLEntity(receivableAccountingGLEntity, transEntity.getAggregateId());
            }

            saveToC2AccountingStatus(aggregateReceivableAccountingTransEntityList, getErrorStatus.apply(sapResponse), aggTransDetails.getGlobalId());
        } catch (Exception e) {
            logger.error("Error storing error results", e);
        }
    }

    @Transactional
    public void storeErrorAndRetry(AggregatedTransaction aggregatedTransaction, AggregatedGeneralLedgerTransactionDetails aggTransDetails,
                                   GeneralLedgerEnvelope sapRequest, SapResponseException sapResponseException, MessageType messageType) {
        this.store5xxErrorResults(aggregatedTransaction, aggTransDetails, sapRequest, sapResponseException, messageType);
        messageQueueProcessor.retryTransactionRequest(aggregatedTransaction);
    }

    private void store5xxErrorResults(AggregatedTransaction aggregatedTransaction, AggregatedGeneralLedgerTransactionDetails aggTransDetails, GeneralLedgerEnvelope sapRequest,
                                      SapResponseException sapResponseException, MessageType messageType) {
        try {
            SapResponseExceptionDto sapResponseExceptionDto = new SapResponseExceptionDto();
            sapResponseExceptionDto.setBody(sapResponseException.getBody());
            sapResponseExceptionDto.setMessage(sapResponseException.getMessage());
            sapResponseExceptionDto.setStatusCode(sapResponseException.getStatusCode());
            List<? extends BaseAggregateReceivableTransEntity> aggregateReceivableAccountingTransEntityList =
                    aggregateReceivableAccountingTransFactory.findByGlobalId(aggTransDetails.getGlobalId(), messageType);
            List<? extends BaseAggregateReceivableTransEntity> filteredAggregateEntities = aggregateReceivableAccountingTransEntityList.stream()
                    .filter(entity -> entity instanceof AggregateReceivableAccountingTransEntity || AppConstants.RecordType.AGGREGATE.equals(((UatpAggregateReceivableAccountingTransEntity) entity).getRecordType()))
                    .toList();

            logger.info("Number of aggregate records found: {}", filteredAggregateEntities.size());

            for (BaseAggregateReceivableTransEntity transEntity : filteredAggregateEntities) {
                ReceivableAccountingGLEntity receivableAccountingGLEntity = recAccGLPostingMapper
                        .mapSap5xxException(sapResponseException, sapRequest, aggTransDetails);
                TransactionType.TYPES transactionType = getTransactionType.apply(aggregatedTransaction);
                receivableAccountingGLEntity.setTransactionType(transactionType != null ? transactionType.toString() : null);
                saveReceivableAccountingGLEntity(receivableAccountingGLEntity, transEntity.getAggregateId());
            }

            saveToC2AccountingStatus(aggregateReceivableAccountingTransEntityList, GlResponseEnum.GLPOSTING_FAILEDCONNECTION.toString(), aggTransDetails.getGlobalId());
        } catch (Exception e) {
            logger.error("Error storing error results", e);
        }
    }

    /**
     * @param receivableAccountingGLEntity
     * @param aggregateId
     */
    private void saveReceivableAccountingGLEntity(ReceivableAccountingGLEntity receivableAccountingGLEntity, String aggregateId) {
        try {
            receivableAccountingGLPostingRepository.save(receivableAccountingGLEntity);
        } catch (Exception e) {
            logger.error("Exception saving ReceivableAccountingGLEntity, aggregateId: {}", aggregateId, e);
        }
    }


    /***
     * @param aggregateEntities
     * @param sapStatus
     */
    private void saveToC2AccountingStatus(List<? extends BaseAggregateReceivableTransEntity> aggregateEntities,
                                          String sapStatus, String globalId) {
        Set<Long> detailIds = aggregateEntities.stream()
                .map(BaseAggregateReceivableTransEntity::getDetailIds)
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        if (!detailIds.isEmpty()) {
            logger.info("Saving AccountStatusEntities for UATP global id {}", globalId);
            retryService.retry(() -> accountingStatusEntityRepository.insertAccountingStatusUatp(sapStatus, globalId));
        } else {
            logger.info("Saving AccountStatusEntities for non-UATP global id {}", globalId);
            retryService.retry(() -> accountingStatusEntityRepository.insertAccountingStatusNonUatp(sapStatus, globalId));
        }
    }

    @Transactional
    public void storeResults(
            AggregatedGeneralLedgerTransactionDetails aggGlDetails,
            GeneralLedgerEnvelope sapRequest,
            GlPostingResponse sapResponse,
            MessageType processType
    ) {

        ArrayList<ReceivableAccountingGLSummaryEntity> summaries = new ArrayList<>();
        ArrayList<ReceivableAccountingGLEntity> details = new ArrayList<>();

        getPostingDetails.apply(sapResponse)
                .ifPresent(detailsList -> detailsList.forEach(deets -> {
                    ReceivableAccountingGLEntity detail = recAccGLPostingMapper.mapSuccessResponse(sapResponse, sapRequest, deets, aggGlDetails);
                    details.add(detail);
                    ReceivableAccountingGLSummaryEntity summary = summaryMapper.map(deets, aggGlDetails);
                    summaries.add(summary);
                }));

        persist(aggGlDetails.getGlobalId(), summaries, details, processType);
    }


    /***
     *
     * @param summaries
     * @param details
     */
    private void persist(String globalId,
                         ArrayList<ReceivableAccountingGLSummaryEntity> summaries,
                         ArrayList<ReceivableAccountingGLEntity> details, MessageType messageType) {
        saveSummaries(summaries);
        savePosting(details);

        List<? extends BaseAggregateReceivableTransEntity> aggregateReceivableAccountingTransEntityList =
                aggregateReceivableAccountingTransFactory.findByGlobalId(globalId, messageType);

        if (!details.isEmpty())
            saveToC2AccountingStatus(aggregateReceivableAccountingTransEntityList, details.getFirst().getGlPostingStatus(), globalId);
    }

    /***
     *
     * @param summaries
     */
    private void saveSummaries(ArrayList<ReceivableAccountingGLSummaryEntity> summaries) {
        for (ReceivableAccountingGLSummaryEntity se : summaries) {
            try {
                this.receivableAccountingGLSummaryRepository.save(se);
            } catch (Exception e) {
                logger.error("Error saving GL summaries", e);
            }
        }
    }

    /***
     *
     */
    private void savePosting(ArrayList<ReceivableAccountingGLEntity> details) {
        for (ReceivableAccountingGLEntity gl : details) {
            try {
                receivableAccountingGLPostingRepository.save(gl);
            } catch (Exception e) {
                logger.error("Error saving GL posting", e);
            }
        }

    }

}
