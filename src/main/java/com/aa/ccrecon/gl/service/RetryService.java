package com.aa.ccrecon.gl.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;


@Service
public class RetryService {

    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Retryable(maxAttemptsExpression = "${retry.times}",
            backoff = @Backoff(delayExpression = "${retry.delay}"))
    public void retry(Runnable runnable) {
        runnable.run();
    }

    @Recover
    public Object catchError(RuntimeException e, Runnable runnable){
        logger.error("Exception occurred: {} - {}", e.getClass().getSimpleName(), e.getMessage());
        throw e;
    }

}