package com.aa.ccrecon.gl.model.sap.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Base entity for response from SAP GL posting service.
 * Contains details about a successful journal posting operation.
 */
@Data
public class PostingDetails {
    
    /**
     * Document ID created in the SAP for the GL posting.
     */
    @JsonProperty("SAPOperationalDocumentID")
    private String sapOperationalDocumentId;
    
    /**
     * SAP Posting amount in the document currency (local currency).
     * Example: "1024.25"
     */
    @JsonProperty("postingAmountLocalCurrency")
    private String postingAmountLocalCurrency;
    
    /**
     * SAP Posting amount in USD.
     */
    @JsonProperty("postingAmountUSD")
    private String postingAmountUsd;
    
    /**
     * Local currency code.
     */
    @JsonProperty("localCurrencyCode")
    private String localCurrencyCode;
    
    /**
     * Company code, max length: 4.
     * Example: "AA"
     */
    @JsonProperty("companyCode")
    private String companyCode;
    
    /**
     * Posting year, max length: 4.
     * Example: "2024"
     */
    @JsonProperty("postingYear")
    private String postingYear;
}