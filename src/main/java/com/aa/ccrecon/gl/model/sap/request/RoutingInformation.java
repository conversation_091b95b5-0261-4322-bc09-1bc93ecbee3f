package com.aa.ccrecon.gl.model.sap.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Represents routing information for a journal posting request.
 */
@Data
public class RoutingInformation {
    
    /**
     * Domain posting the Journal Entry
     */
    @JsonProperty("Domain")
    private String domain;
    
    /**
     * SAP PI Interface name used to post
     */
    @JsonProperty("Interface")
    private String interfaceName;
    
    /**
     * Operational System posting to the General ledger
     */
    @JsonProperty("Source")
    private String source;
    
    /**
     * Destination system name which is SAP
     */
    @JsonProperty("Destination")
    private String destination;
    
    /**
     * Source Webservice name posting to the ledger
     */
    @JsonProperty("Service")
    private String service;
    
    /**
     * Transaction type, example: "BAPI"
     */
    @JsonProperty("TransType")
    private String transType;
    
    /**
     * SAP document type
     * Values for Amadeus RA posting:
     * Q1 - Sales
     * Q2 - Uplift
     * Q3 - Inward
     * Q4 - Outward
     * Q5 - Agency Debit Memo
     * Q6 - Agency Credit Memo
     */
    @JsonProperty("SapDocType")
    private String sapDocType;
    
    /**
     * User or system ID posting the journal entry
     */
    @JsonProperty("User_ID")
    private String userId;
    
    /**
     * Date of journal is getting posted
     */
    @JsonProperty("Date_Updated")
    private String dateUpdated;
    
    /**
     * Time the journal is getting posted
     */
    @JsonProperty("Time_Updated")
    private String timeUpdated;

    @JsonProperty("Global_ID")
    private String globalId;
}