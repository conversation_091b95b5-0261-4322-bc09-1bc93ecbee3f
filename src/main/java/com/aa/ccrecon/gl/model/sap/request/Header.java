package com.aa.ccrecon.gl.model.sap.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Represents the header information for a SAP General Ledger journal posting request.
 * Contains control information for the journal entry processing.
 */
@Data
public class Header {
    
    /**
     * Execution mode for the request.
     * Example: "S"
     */
    @JsonProperty("EXEC_MODE")
    private String execMode;
    
    /**
     * Processing option for the request.
     * Example: "P"
     */
    @JsonProperty("PROC_OPTION")
    private String procOption;
    
    /**
     * Identifier for the journal entry.
     */
    @JsonProperty("IDENTIFIER")
    private String identifier;
    
    /**
     * Split line indicator.
     */
    @JsonProperty("SPLIT_LINE")
    private String splitLine;
    
    /**
     * Reversal indicator.
     */
    @JsonProperty("REVERSAL")
    private String reversal;
    
    /**
     * Date for the reversal if applicable.
     * Format should match SAP date format.
     */
    @JsonProperty("REVERSAL_DATE")
    private String reversalDate;
    
    /**
     * Reason for the reversal if applicable.
     */
    @JsonProperty("Rev_reason")
    private String revReason;
}