package com.aa.ccrecon.gl.model.request;

import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import com.google.gson.annotations.SerializedName;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoutingInfo {

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP Domain can not be null")
    @Length(groups={AggregateGLValidation.class}, min=1, max = 15, message = "SAP Domain '${validatedValue}' must be between 1 and 15 characters")
    private String domain;

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP InterFace can not be null")
    @Length(groups = {AggregateGLValidation.class}, min=1, max = 30, message = "SAP InterFace '${validatedValue}' must be between 1 and 30 characters")
    @SerializedName(value = "interface")
    private String interFace;

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP Source can not be null")
    @Length(groups = {AggregateGLValidation.class}, min=1, max = 5, message = "'SAP Source ${validatedValue}' must be between 1 and 5 characters")
    private String source;

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP Destination can not be null")
    @Length(groups = {AggregateGLValidation.class}, min=1, max = 5, message = "SAP Destination '${validatedValue}' must be between 1 and 5 characters")
    private String destination;

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP Service can not be null")
    @Length(groups = {AggregateGLValidation.class}, min=1, max = 10, message = "SAP Service '${validatedValue}' must be between 1 and 10 characters")
    private String service;

    @NotNull(groups={AggregateGLValidation.class}, message = "SAP TransType can not be null")
    @Length(groups = {AggregateGLValidation.class}, min=1, max = 5, message = "SAP TransType '${validatedValue}' must be between 1 and 5 characters")
    private String transType;

    @NotNull(groups={AggregateGLValidation.class}, message = "SapDocType can not be null")
    @Length(groups = {AggregateGLValidation.class}, min = 1, max = 5, message = "SapDocType '${validatedValue}' must be between 1 and 5 characters")
    private String sapDocType;

    @Length(groups = {AggregateGLValidation.class}, max = 50, message = "UserID '${validatedValue}' must be less than max 50")
    private String userId;

    private String dateUpdated;
    private String timeUpdated;

    @NotNull(groups={AggregateGLValidation.class}, message = "Global Id can not be null")
    @Length(groups ={AggregateGLValidation.class}, min=1, max = 50, message = "Global Id '${validatedValue}' must be between 1 and 50 characters")
    private String globalUniqueId;
   
}
