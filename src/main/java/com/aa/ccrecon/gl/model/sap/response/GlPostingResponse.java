package com.aa.ccrecon.gl.model.sap.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Represents the response from the SAP GL journal posting service.
 */
@Data
public class GlPostingResponse {

    /**
     * Status of the posting operation.
     * Possible values: "Success", "Failure"
     */
    @JsonProperty("postingStatus")
    private PostingStatus postingStatus;

    /**
     * Details of the successful posting operations.
     */
    @JsonProperty("postingDetails")
    private List<PostingDetails> postingDetails;

    /**
     * Error messages if the posting operation failed.
     */
    @JsonProperty("errorMessages")
    private List<ErrorLogMessage> errorMessages;
}
