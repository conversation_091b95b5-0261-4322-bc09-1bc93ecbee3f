package com.aa.ccrecon.gl.model.request;

import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class Body {

    @Valid
    @NotNull(groups={AggregateGLValidation.class}, message = "PostGeneralLedgerRq must not be null")
    private PostGeneralLedgerRq postGeneralLedgerRq;

}
