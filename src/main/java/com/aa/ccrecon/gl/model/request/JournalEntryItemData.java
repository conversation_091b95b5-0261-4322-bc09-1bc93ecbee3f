package com.aa.ccrecon.gl.model.request;

import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import com.aa.itfacs.pmt.mask.annotation.Mask;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class JournalEntryItemData {

        private String sequenceNo;


        @NotNull(groups = { AggregateGLValidation.class }, message = "DocType can not be null")
        @Length(groups = {AggregateGLValidation.class }, max = 2, message = "DocType '${validatedValue}' must be less than 2 characters")
        private String docType;

        @NotNull(groups = { AggregateGLValidation.class }, message = "CompanyCode can not be null")
        @Length(groups = {AggregateGLValidation.class }, min=1, max = 4, message = "CompanyCode '${validatedValue}' must be between 1 and 4 characters")
        private String companyCode;

        @NotNull(groups = { AggregateGLValidation.class }, message = "Currency can not be null")
        @Length(groups = {AggregateGLValidation.class }, min=1, max = 3, message = "Currency '${validatedValue}' must be between 1 and 3 characters")
        private String currency;
        private String paymentMethodSupplement;
        private String postingDate;
        private String fiscalPeriod;

        @NotNull(groups = { AggregateGLValidation.class }, message = "DocumentHeaderText can not be null")
        @Length(groups = {AggregateGLValidation.class }, min=1, max = 25, message = "DocumentHeaderText '${validatedValue}' must be between 1 and 25 characters.")
        private String documentHeaderText;

        private String newCompanyCode;

        private String referenceDocumentnumber;
        private String documentDate;
        private String postingKey;

        @NotNull(groups = { AggregateGLValidation.class }, message = "Account Number can not be null")
        @Length(groups = {
                        AggregateGLValidation.class }, min=1, max = 10, message = "Account Number '${validatedValue}' must be between 1 and 10 characters")
        private String accountNumber;
        private String customerNumber;
        private String vendor;

        @NotNull(groups = { AggregateGLValidation.class }, message = "Amount can not be null")
        @Length(groups = {AggregateGLValidation.class }, max = 16, message = "Amount '${validatedValue}' must be between 1 and 16 characters")
        @Pattern(groups = {AggregateGLValidation.class }, regexp = "^[0-9]{13}\\.[0-9]{2}$", message = "Amount not in correct pattern for a number")
        private String amountInDocumentCurrency;
        private String taxCode;
        private String paymentTerms;
        private String paymentMethod;
        private String costCenter;
        private String profitCenter;

        @Mask
        private String assignmentNumber;

        @NotNull(groups = { AggregateGLValidation.class }, message = "Item Text can not be null")
        @Length(groups = {AggregateGLValidation.class }, min=1,max = 50, message = "'${validatedValue}' must be between 1 and 50 characters")
        private String itemText;
        private String orderNumber;
        private String networkNumber;
        private String purchaseOrderNumber;
        private String purchaseOrderItemNumber;
        private String quantity;
        private String baseUnitOfMeasure;
        private String valueDate;
        private String busPartnerReferenceKey;
        private String assetSubNumber;
        private String personnelNumber;
        private String businessArea;
        private String taxAmount;
        private String tradingPartnerId;
        private String amountInLocalCurrency;
        private String amountInSecondLocalCurrency;
        private String assetTransactionType;
        private String exchangeRaate;
        private String dats;
        private String assetValueDate;
        private String taxJurisdiction;
        private String materialNumber;
        private String businessPlace;
        private String invoiceNumber;
        private String invoiceFiscalYear;
        private String invoiceLineItem;
        private String referenceKey1;
        private String referenceKey2;
        private String referenceKey3;
        private String localCurrency;
        private String groupCurrency;
        private String longText;

}