package com.aa.ccrecon.gl.model.sap.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Represents an individual journal entry item.
 */
@Data
public class JournalEntryItem {

    /**
     * SAP document type
     * Values for Amadeus RA posting:
     * Q1 - Sales
     * Q2 - Uplift
     * Q3 - Inward
     * Q4 - Outward
     * Q5 - Agency Debit Memo
     * Q6 - Agency Credit Memo
     */
    @JsonProperty("DOC_TYPE")
    private String docType;

    /**
     * Company Code, max length: 4
     */
    @JsonProperty("COMP_CODE")
    private String compCode;

    /**
     * Currency code for posting, max length: 3
     */
    @JsonProperty("CURRENCY")
    private String currency;

    /**
     * Payment Method Supplement (N/A for ARA)
     */
    @JsonProperty("PMTMTHSUPL")
    private String paymentMethodSupplement;

    /**
     * GL Posting date. Format is CCYYMMDD
     */
    @JsonProperty("PSTNG_DATE")
    private String postingDate;

    /**
     * Month of the Posting Date is the fiscal period, max length: 2
     */
    @JsonProperty("FIS_PERIOD")
    private String fiscalPeriod;

    /**
     * Header text, max length: 25
     */
    @JsonProperty("HEADER_TXT")
    private String headerTxt;

    /**
     * Company code from the posting entry, max length: 4
     */
    @JsonProperty("NEWCOMP")
    private String newComp;

    /**
     * Reference Document Number
     */
    @JsonProperty("REF_DOC_NO")
    private String refDocNo;

    /**
     * Document date
     */
    @JsonProperty("DOC_DATE")
    private String docDate;

    /**
     * Post key, max length: 4
     * 40 - Credit
     * 50 - Debit
     */
    @JsonProperty("POST_KEY")
    private String postKey;

    /**
     * General ledger Account to post the entry, max length: 10
     */
    @JsonProperty("ACCOUNT_NO")
    private String accountNo;

    /**
     * Customer number, max length: 10
     */
    @JsonProperty("CUSTOMER")
    private String customer;

    /**
     * Vendor Id, max length: 10
     */
    @JsonProperty("VENDOR")
    private String vendor;

    /**
     * Transaction currency. For certain type of GL will be also populated in account currency.
     * Length 13 with precision 2 decimals in String format
     */
    @JsonProperty("AMT_DOC_CURR")
    private String amtDocCurr;

    /**
     * Tax on sales/purchases code, max length: 2
     */
    @JsonProperty("TAX_CODE")
    private String taxCode;

    /**
     * Terms of payment key, max length: 4
     */
    @JsonProperty("PMNTTRMS")
    private String paymentTerms;

    /**
     * Payment method, max length: 1
     */
    @JsonProperty("PYMT_METH")
    private String paymentMethod;

    /**
     * Cost center for the Journal posting, max length: 10
     */
    @JsonProperty("COSTCENTER")
    private String costCenter;

    /**
     * Profit center, max length: 10
     * If there is a revenue entry, a profit center should be provided instead of a cost center.
     * It is essentially the same as a cost center but cost centers are for the expenses and
     * profit centers for revenues
     */
    @JsonProperty("PROFIT_CTR")
    private String profitCtr;

    /**
     * Allocation number, max length: 18
     */
    @JsonProperty("ALLOC_NMBR")
    private String allocNmbr;

    /**
     * Details of the posting item, max length: 50
     */
    @JsonProperty("ITEM_TEXT")
    private String itemText;

    /**
     * Order number, max length: 12
     */
    @JsonProperty("ORDERID")
    private String orderId;

    /**
     * Network number for account assignment, max length: 12
     */
    @JsonProperty("NETWORK")
    private String network;

    /**
     * Purchasing document number, max length: 10
     */
    @JsonProperty("PO_NUMBER")
    private String poNumber;

    /**
     * Item number of purchasing document, max length: 5
     */
    @JsonProperty("PO_ITEM")
    private String poItem;

    /**
     * Quantity of items purchased, max length: 17
     */
    @JsonProperty("QUANTITY")
    private String quantity;

    /**
     * Base unit of measure, max length: 3
     */
    @JsonProperty("BASE_UOM")
    private String baseUom;

    /**
     * Transaction date in CCYYMMDD format, max length: 8
     */
    @JsonProperty("VALUE_DATE")
    private String valueDate;

    /**
     * Business partner reference key value, max length: 12
     */
    @JsonProperty("ZASSET")
    private String zAsset;

    /**
     * Asset sub number, max length: 4
     */
    @JsonProperty("Asset_SUB_NUMBER")
    private String assetSubNumber;

    /**
     * Personnel or user id, max length: 10
     */
    @JsonProperty("PERSONNEL_NO")
    private String personnelNo;

    /**
     * Business Area, max length: 4
     */
    @JsonProperty("BUS_AREA")
    private String busArea;

    /**
     * Tax amount 16 char length with 0 precision in document currency
     */
    @JsonProperty("TAX_AMT")
    private String taxAmt;

    /**
     * Company Id of the trading Partner, max length: 6
     */
    @JsonProperty("TRADE_ID")
    private String tradeId;

    /**
     * Amount in local currency, length 13 precision 2
     */
    @JsonProperty("AMT_LOCALCUR")
    private String amtLocalCur;

    /**
     * Amount in second local currency, length 16 precision 0
     */
    @JsonProperty("AMT_SECONDLOCALCUR")
    private String amtSecondLocalCur;

    /**
     * Asset transaction type, max length: 3
     */
    @JsonProperty("ASSET_TRANSTYPE")
    private String assetTransType;

    /**
     * Exchange rate direct quotation, max length: 10
     */
    @JsonProperty("EXCH_RATE")
    private String exchangeRate;

    /**
     * Field of type DATS, max length: 8
     */
    @JsonProperty("DATS")
    private String dats;

    /**
     * Asset value date, max length: 8
     */
    @JsonProperty("ASVAL_DATE")
    private String assetValueDate;

    /**
     * Tax jurisdiction code, max length: 15
     */
    @JsonProperty("TAXJURCODE")
    private String taxJurCode;

    /**
     * Material Number, max length: 18
     */
    @JsonProperty("MATERIAL")
    private String material;

    /**
     * Business place, max length: 4
     */
    @JsonProperty("BUSINESSPLACE")
    private String businessPlace;

    /**
     * Invoice number, max length: 10
     */
    @JsonProperty("INVOICE_NO")
    private String invoiceNo;

    /**
     * Fiscal Year of the Relevant Invoice (for Credit Memo), max length: 4
     */
    @JsonProperty("INVOICE_FISCAL_YEAR")
    private String invoiceFiscalYear;

    /**
     * Line Item in the Relevant Invoice (Batch Input), max length: 3
     */
    @JsonProperty("INVOICE_LINEITEM")
    private String invoiceLineItem;

    /**
     * Business partner reference key 1, max length: 12
     */
    @JsonProperty("REF_KEY_1")
    private String refKey1;

    /**
     * Business partner reference key 2, max length: 12
     */
    @JsonProperty("REF_KEY_2")
    private String refKey2;

    /**
     * Business partner reference key 3, max length: 12
     */
    @JsonProperty("REF_KEY_3")
    private String refKey3;

    /**
     * Local currency code, max length: 5
     */
    @JsonProperty("CURR_LOCAL")
    private String currLocal;

    /**
     * Currency Code of the Secondary Local currency, max length: 5
     */
    @JsonProperty("CURR_GROUP")
    private String currGroup;

    /**
     * Long text, max length: 300
     */
    @JsonProperty("LONG_TEXT")
    private String longText;
}
