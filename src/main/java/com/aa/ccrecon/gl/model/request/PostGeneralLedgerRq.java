package com.aa.ccrecon.gl.model.request;

import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class PostGeneralLedgerRq {

	@Valid
	@NotNull(groups={AggregateGLValidation.class}, message = "JournalDocument must not be null")
	private JournalDocument journalDocument;

}