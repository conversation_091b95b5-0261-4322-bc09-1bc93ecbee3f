package com.aa.ccrecon.gl.model.request;

import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import com.google.gson.annotations.SerializedName;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SapRequest {

    @Valid
    @NotNull(groups={AggregateGLValidation.class}, message = "'PostGeneralLedgerRqEnvelope' must not be null")
    @SerializedName(value = "PostGeneralLedgerRqEnvelope")
    PostGeneralLedgerRqEnvelope postGeneralLedgerRqEnvelope;

}