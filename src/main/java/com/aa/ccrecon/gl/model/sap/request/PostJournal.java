package com.aa.ccrecon.gl.model.sap.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Represents the journal posting details in a General Ledger posting request.
 * This class maps to the PostJournal schema in the OpenAPI specification.
 */
@Data
public class PostJournal {

    /**
     * Header information for the journal posting.
     */
    @JsonProperty("Header")
    private Header header;

    /**
     * Routing information for the journal posting.
     */
    @JsonProperty("Routing_Info")
    private RoutingInformation routingInfo;

    /**
     * List of journal entry items representing the detailed financial transactions.
     */
    @JsonProperty("PostJournal_Detail")
    private List<JournalEntryItem> postJournalDetail;
}
