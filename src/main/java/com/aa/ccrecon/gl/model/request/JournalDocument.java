package com.aa.ccrecon.gl.model.request;

import java.util.List;

import com.google.gson.annotations.SerializedName;
import com.aa.ccrecon.gl.validation.AggregateGLValidation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class JournalDocument {

    @Valid
    @NotNull(groups={AggregateGLValidation.class}, message = "RoutingInfo must not be null")
    private RoutingInfo routingInfo;

    private Header header;

    @Valid
    @NotNull(groups={AggregateGLValidation.class}, message = "JournalEntryItems must not be null")
    @SerializedName("journalEntryItems")
    private List<JournalEntryItem> journalEntryItems;



}
