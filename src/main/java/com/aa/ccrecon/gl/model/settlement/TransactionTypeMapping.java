package com.aa.ccrecon.gl.model.settlement;

import com.aa.ccrecon.gl.model.AccountType;

/**
 * Data class representing the mapping of a transaction type to its
 * debit/credit designation and post key for settlement accounting.
 */
public class TransactionTypeMapping {
    private final AccountType debitCredit;
    private final String postKey;

    public TransactionTypeMapping(AccountType debitCredit, String postKey) {
        this.debitCredit = debitCredit;
        this.postKey = postKey;
    }

    public AccountType getDebitCredit() {
        return debitCredit;
    }

    public String getPostKey() {
        return postKey;
    }

    @Override
    public String toString() {
        return String.format("TransactionTypeMapping{debitCredit=%s, postKey='%s'}", debitCredit, postKey);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TransactionTypeMapping that = (TransactionTypeMapping) o;
        return debitCredit == that.debitCredit && postKey.equals(that.postKey);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(debitCredit, postKey);
    }
}
