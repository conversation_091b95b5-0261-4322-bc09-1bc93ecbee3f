package com.aa.ccrecon.gl.validation;

import com.aa.ccrecon.gl.model.request.JournalEntryItemData;
import com.aa.ccrecon.gl.model.request.SapRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class ValidationFactory {

    @Autowired
    private Validator validator;

    /***
     *
     * @param model
     * @param clazz
     * @return
     */
    @SafeVarargs
    public final <T> Set<ConstraintViolation<SapRequest>> validateSapRequest(SapRequest model, Class<T>... clazz) {
        return validator.validate(model, clazz);
    }

    @SafeVarargs
    public final <T> Set<ConstraintViolation<JournalEntryItemData>> validateJournalItem(JournalEntryItemData model, Class<T>... clazz) {
        return validator.validate(model, clazz);
    }


}
