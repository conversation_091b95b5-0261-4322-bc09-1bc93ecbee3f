package com.aa.ccrecon.gl.validation;

import com.aa.ccrecon.gl.model.request.JournalEntryItem;
import com.aa.ccrecon.gl.model.request.JournalEntryItemData;
import com.aa.ccrecon.gl.model.request.SapRequest;
import jakarta.validation.ConstraintViolation;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class ValidationService {
    private final ValidationFactory validationFactory;

    private final Logger logger = LoggerFactory.getLogger(ValidationService.class);

    public ValidationService (ValidationFactory validationFactory) {
        this.validationFactory = validationFactory;
    }



    public void printValidationErrors(SapRequest sapRequest) {
        /// validate request
        Set<ConstraintViolation<SapRequest>> errors = validationFactory.validateSapRequest(sapRequest, AggregateGLValidation.class);
        for (ConstraintViolation<SapRequest> err : errors) {
            logger.warn("Error in validation {}", err.getMessage());
        }

        printItemErrors(sapRequest, "40", AggregateGL40Validation.class);
        printItemErrors(sapRequest, "50", AggregateGL50Validation.class);

    }

    public <T> void printItemErrors(SapRequest sapRequest, String type, Class<T> clazz) {

        List<JournalEntryItem> items = sapRequest
                .getPostGeneralLedgerRqEnvelope()
                .getBody()
                .getPostGeneralLedgerRq()
                .getJournalDocument()
                .getJournalEntryItems();

        for (JournalEntryItem item : items) {
            JournalEntryItemData data = item.getJournalEntryItemData();
            if (data.getPostingKey().equalsIgnoreCase( type )) {

                Set<ConstraintViolation<JournalEntryItemData>> fortyErr = validationFactory.validateJournalItem(data, clazz);
                for (ConstraintViolation<JournalEntryItemData> err : fortyErr) {
                    logger.warn("Error in validation {}", err.getMessage());
                }

            }
        }

    }


}
