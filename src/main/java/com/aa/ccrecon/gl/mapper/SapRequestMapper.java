package com.aa.ccrecon.gl.mapper;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.constants.DocType;
import com.aa.ccrecon.gl.config.SapRequestConfig;
import com.aa.ccrecon.gl.entity.AggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.entity.BaseAggregateReceivableTransEntity;
import com.aa.ccrecon.gl.entity.UatpAggregateReceivableAccountingTransEntity;
import com.aa.ccrecon.gl.model.AccountType;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.utils.AppConstants;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.text.DecimalFormat;
import java.util.List;

import static com.aa.ccrecon.gl.utils.ModelExtractor.addJournalEntryItems;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SapRequestMapper {

    @Mapping(target = "postJournal.header.execMode", source = "sapRequestConfig.execMode")
    @Mapping(target = "postJournal.header.procOption", source = "sapRequestConfig.procOption")
    @Mapping(target = "postJournal.routingInfo.domain", source = "sapRequestConfig.domain")
    @Mapping(target = "postJournal.routingInfo.interfaceName", source = "sapRequestConfig.interFace")
    @Mapping(target = "postJournal.routingInfo.source", source = "sapRequestConfig.source")
    @Mapping(target = "postJournal.routingInfo.destination", source = "sapRequestConfig.destination")
    @Mapping(target = "postJournal.routingInfo.service", source = "sapRequestConfig.service")
    @Mapping(target = "postJournal.routingInfo.transType", source = "sapRequestConfig.transtype")
    @Mapping(target = "postJournal.routingInfo.sapDocType", source = "sapRequestConfig.docType")
    @Mapping(target = "postJournal.routingInfo.userId", source = "sapRequestConfig.userId")
    @Mapping(target = "postJournal.routingInfo.globalId", source = "aggregatedTransationDto.globalId")
    @Mapping(target = "postJournal.routingInfo.dateUpdated", expression = "java(java.time.LocalDateTime.now(java.time.ZoneOffset.UTC).format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "postJournal.routingInfo.timeUpdated", expression = "java(java.time.LocalDateTime.now(java.time.ZoneOffset.UTC).format(java.time.format.DateTimeFormatter.ofPattern(\"HHmmss\")))")
    GeneralLedgerEnvelope mapGlEnvelope(List<AggregateReceivableAccountingTransEntity> aggRecAcctTransEntityList, AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto, SapRequestConfig sapRequestConfig);

    @AfterMapping
    default void mapGlEnvelopeAfter(@MappingTarget GeneralLedgerEnvelope glEnvelope,
                                    List<AggregateReceivableAccountingTransEntity> aggRecAcctTransEntityList,
                                    AggregatedGeneralLedgerTransactionDetails aggTransDto,
                                    SapRequestConfig sapReqConfig) {
        for (AggregateReceivableAccountingTransEntity aggTransEntity : aggRecAcctTransEntityList) {
            com.aa.ccrecon.gl.model.sap.request.JournalEntryItem debitEntry = mapJournalEntryItem(aggTransEntity, aggTransDto, sapReqConfig, AccountType.DEBIT);
            com.aa.ccrecon.gl.model.sap.request.JournalEntryItem creditEntry = mapJournalEntryItem(aggTransEntity, aggTransDto, sapReqConfig, AccountType.CREDIT);
            if (sapReqConfig.getDocType().equals(DocType.R9.name())) {
                creditEntry.setAllocNmbr(aggTransEntity.getReconId());
            } else {
                debitEntry.setAllocNmbr(aggTransEntity.getReconId());
            }
            addJournalEntryItems.accept(glEnvelope, List.of(debitEntry, creditEntry));
        }
    }

    @Mapping(target = "postJournal.header.execMode", source = "sapRequestConfig.execMode")
    @Mapping(target = "postJournal.header.procOption", source = "sapRequestConfig.procOption")
    @Mapping(target = "postJournal.routingInfo.domain", source = "sapRequestConfig.domain")
    @Mapping(target = "postJournal.routingInfo.interfaceName", source = "sapRequestConfig.interFace")
    @Mapping(target = "postJournal.routingInfo.source", source = "sapRequestConfig.source")
    @Mapping(target = "postJournal.routingInfo.destination", source = "sapRequestConfig.destination")
    @Mapping(target = "postJournal.routingInfo.service", source = "sapRequestConfig.service")
    @Mapping(target = "postJournal.routingInfo.transType", source = "sapRequestConfig.transtype")
    @Mapping(target = "postJournal.routingInfo.sapDocType", source = "sapRequestConfig.docType")
    @Mapping(target = "postJournal.routingInfo.userId", source = "sapRequestConfig.userId")
    @Mapping(target = "postJournal.routingInfo.globalId", source = "aggregatedTransationDto.globalId")
    @Mapping(target = "postJournal.routingInfo.dateUpdated", expression = "java(java.time.LocalDateTime.now(java.time.ZoneOffset.UTC).format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "postJournal.routingInfo.timeUpdated", expression = "java(java.time.LocalDateTime.now(java.time.ZoneOffset.UTC).format(java.time.format.DateTimeFormatter.ofPattern(\"HHmmss\")))")
    GeneralLedgerEnvelope mapGlEnvelopeUatp(List<UatpAggregateReceivableAccountingTransEntity> aggRecAcctTransEntityList, AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto, SapRequestConfig sapRequestConfig);

    @AfterMapping
    default void mapGlEnvelopeAfterUatp(@MappingTarget GeneralLedgerEnvelope glEnvelope,
                                        List<UatpAggregateReceivableAccountingTransEntity> uatpAggRecAcctTransEntityList,
                                        AggregatedGeneralLedgerTransactionDetails aggTransDto,
                                        SapRequestConfig sapReqConfig) {
        for (UatpAggregateReceivableAccountingTransEntity uatpEntity : uatpAggRecAcctTransEntityList) {
            com.aa.ccrecon.gl.model.sap.request.JournalEntryItem jei = null;
            boolean isR9DocType = sapReqConfig.getDocType() != null && sapReqConfig.getDocType().equals(DocType.R9.name());
            if (uatpEntity.getRecordType().equals(AppConstants.RecordType.DETAIL)) {
                AccountType accountType = isR9DocType ? AccountType.CREDIT : AccountType.DEBIT;
                jei = mapJournalEntryItemUatp(uatpEntity, aggTransDto, sapReqConfig, accountType);
                jei.setAllocNmbr(uatpEntity.getReceivableAccountingTransDetailsEntity().getTicketNumber());
                if (isR9DocType) {
                    jei.setPostKey(sapReqConfig.getPostingKeyCredit());
                    jei.setAccountNo(uatpEntity.getCreditAccount());
                } else {
                    jei.setPostKey(sapReqConfig.getPostingKeyDebit());
                    jei.setAccountNo(uatpEntity.getDebitAccount());
                }
            } else if (uatpEntity.getRecordType().equals(AppConstants.RecordType.AGGREGATE)) {
                AccountType accountType = isR9DocType ? AccountType.DEBIT : AccountType.CREDIT;
                jei = mapJournalEntryItemUatp(uatpEntity, aggTransDto, sapReqConfig, accountType);
                jei.setRefDocNo(uatpEntity.getPostToLedger());
                if (isR9DocType) {
                    jei.setPostKey(sapReqConfig.getPostingKeyDebit());
                    jei.setAccountNo(uatpEntity.getDebitAccount());
                } else {
                    jei.setPostKey(sapReqConfig.getPostingKeyCredit());
                    jei.setAccountNo(uatpEntity.getCreditAccount());
                }
            } else {
                return;
            }
            addJournalEntryItems.accept(glEnvelope, List.of(jei));
        }
    }

    @Mapping(target = "docType", source = "sapRequestConfig.docType")
    @Mapping(target = "compCode", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? aggRecAcctTransEntity.getDebitCompanyCode() : aggRecAcctTransEntity.getCreditCompanyCode())")
    @Mapping(target = "currency", source = "aggRecAcctTransEntity.currency")
    @Mapping(target = "postingDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "valueDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "docDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "fiscalPeriod", expression = "java(String.format(\"%2s\", java.time.LocalDate.now().getMonthValue()).replace(' ', '0'))")
    @Mapping(target = "headerTxt", source = "sapRequestConfig.documentHeaderText")
    @Mapping(target = "postKey", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? sapRequestConfig.getPostingKeyDebit() : sapRequestConfig.getPostingKeyCredit())")
    @Mapping(target = "accountNo", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? aggRecAcctTransEntity.getDebitAccount() : aggRecAcctTransEntity.getCreditAccount())")
    com.aa.ccrecon.gl.model.sap.request.JournalEntryItem mapJournalEntryItem(AggregateReceivableAccountingTransEntity aggRecAcctTransEntity,
                                                                             AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto,
                                                                             SapRequestConfig sapRequestConfig,
                                                                             AccountType accountType);

    @Mapping(target = "docType", source = "sapRequestConfig.docType")
    @Mapping(target = "compCode", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? aggRecAcctTransEntity.getDebitCompanyCode() : aggRecAcctTransEntity.getCreditCompanyCode())")
    @Mapping(target = "currency", source = "aggRecAcctTransEntity.currency")
    @Mapping(target = "postingDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "valueDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "docDate", expression = "java(java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern(\"yyyyMMdd\")))")
    @Mapping(target = "fiscalPeriod", expression = "java(String.format(\"%2s\", java.time.LocalDate.now().getMonthValue()).replace(' ', '0'))")
    @Mapping(target = "headerTxt", source = "sapRequestConfig.documentHeaderText")
    @Mapping(target = "postKey", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? sapRequestConfig.getPostingKeyDebit() : sapRequestConfig.getPostingKeyCredit())")
    @Mapping(target = "accountNo", expression = "java(accountType == com.aa.ccrecon.gl.model.AccountType.DEBIT ? aggRecAcctTransEntity.getDebitAccount() : aggRecAcctTransEntity.getCreditAccount())")
    com.aa.ccrecon.gl.model.sap.request.JournalEntryItem mapJournalEntryItemUatp(UatpAggregateReceivableAccountingTransEntity aggRecAcctTransEntity,
                                                                                 AggregatedGeneralLedgerTransactionDetails aggregatedTransationDto,
                                                                                 SapRequestConfig sapRequestConfig,
                                                                                 AccountType accountType);

    @AfterMapping
    default void mapNonUtapCurrency(@MappingTarget com.aa.ccrecon.gl.model.sap.request.JournalEntryItem jei,
                                    SapRequestConfig sapRequestConfig,
                                    AggregateReceivableAccountingTransEntity aggRecAcctTransEntity,
                                    AccountType accountType) {
        DecimalFormat df = new DecimalFormat(sapRequestConfig.getDecimalFormat());
        jei.setAmtDocCurr(df.format((roundAvoid(aggRecAcctTransEntity.getSubTotal().doubleValue(), 2))));
        jei.setItemText(aggRecAcctTransEntity.getLineItemText());
        jei.setRefDocNo(aggRecAcctTransEntity.getPostToLedger());
        jei.setAmtLocalCur(calcLocalCurrencyAmount(aggRecAcctTransEntity, accountType, df));
        jei.setAmtSecondLocalCur(df.format((roundAvoid(aggRecAcctTransEntity.getConvertedSubtotalAmount().doubleValue(), 2))));
        jei.setCurrLocal(isCompanyCodeAA00(aggRecAcctTransEntity, accountType) ?
                aggRecAcctTransEntity.getConvertedCurrency() : aggRecAcctTransEntity.getCurrency());
        jei.setCurrGroup(aggRecAcctTransEntity.getConvertedCurrency());
    }

    @AfterMapping
    default void mapUtapCurrency(@MappingTarget com.aa.ccrecon.gl.model.sap.request.JournalEntryItem jei,
                                 SapRequestConfig sapRequestConfig,
                                 UatpAggregateReceivableAccountingTransEntity uatpAggTransEntity,
                                 AccountType accountType) {
        DecimalFormat df = new DecimalFormat(sapRequestConfig.getDecimalFormat());
        jei.setAmtDocCurr(df.format((roundAvoid(uatpAggTransEntity.getAmount().doubleValue(), 2))));
        jei.setItemText(uatpAggTransEntity.getLineItemText());
        jei.setRefDocNo(uatpAggTransEntity.getPostToLedger());
        jei.setAmtLocalCur(calcLocalCurrencyAmount(uatpAggTransEntity, accountType, df));
        jei.setAmtSecondLocalCur(df.format((roundAvoid(uatpAggTransEntity.getConvertedAmount().doubleValue(), 2))));
        jei.setCurrLocal(isCompanyCodeAA00(uatpAggTransEntity, accountType) ?
                uatpAggTransEntity.getConvertedCurrency() : uatpAggTransEntity.getCurrency());
        jei.setCurrGroup(uatpAggTransEntity.getConvertedCurrency());
    }

    default String calcLocalCurrencyAmount(AggregateReceivableAccountingTransEntity aggregatedTransaction, AccountType accountType, DecimalFormat df) {
        if (isCompanyCodeAA00(aggregatedTransaction, accountType)) {
            return String.valueOf(df.format((roundAvoid(aggregatedTransaction.getConvertedSubtotalAmount().doubleValue(), 2))));
        } else {
            return String.valueOf(df.format((roundAvoid(aggregatedTransaction.getSubTotal().doubleValue(), 2))));
        }
    }

    default String calcLocalCurrencyAmount(UatpAggregateReceivableAccountingTransEntity uatpAggEntity, AccountType accountType, DecimalFormat df) {
        if (isCompanyCodeAA00(uatpAggEntity, accountType)) {
            return String.valueOf(df.format((roundAvoid(uatpAggEntity.getConvertedAmount().doubleValue(), 2))));
        } else {
            return String.valueOf(df.format((roundAvoid(uatpAggEntity.getAmount().doubleValue(), 2))));
        }
    }

    default boolean isCompanyCodeAA00(BaseAggregateReceivableTransEntity baseAggEntity, AccountType accountType) {
        var compCode = accountType == AccountType.DEBIT ? baseAggEntity.getDebitCompanyCode() : baseAggEntity.getCreditCompanyCode();
        return compCode != null && compCode.equals("AA00");
    }

    /***
     *
     * @param value
     * @param places
     * @return
     */
    private static double roundAvoid(double value, int places) {
        double scale = Math.pow(10, places);
        return Math.round(value * scale) / scale;
    }

}

