package com.aa.ccrecon.gl.mapper;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLEntity;
import com.aa.ccrecon.gl.exception.SapResponseException;
import com.aa.ccrecon.gl.model.request.SapRequest;
import com.aa.ccrecon.gl.model.response.ErrorItemData;
import com.aa.ccrecon.gl.model.response.SapResponse;
import com.aa.ccrecon.gl.model.sap.request.GeneralLedgerEnvelope;
import com.aa.ccrecon.gl.model.sap.response.GlPostingResponse;
import com.aa.ccrecon.gl.model.sap.response.PostingDetails;
import com.google.gson.Gson;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.sql.Timestamp;
import java.time.Instant;

import static com.aa.ccrecon.gl.model.GlResponseEnum.GLPOSTING_ERROR;
import static com.aa.ccrecon.gl.utils.ModelExtractor.getErrorStatus;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        imports = {com.aa.ccrecon.gl.model.GlResponseEnum.class, com.aa.ccrecon.gl.utils.ModelExtractor.class}
)
public interface RecAccGLPostingMapper {

    @Mapping(target = "sapDocNumber", source = "postingDetails.sapOperationalDocumentId")
    @Mapping(target = "companyCode", source = "postingDetails.companyCode")
    @Mapping(target = "glPostYear", source = "postingDetails.postingYear")
    @Mapping(target = "glPostingStatus", expression = "java(GlResponseEnum.GLPOSTING_SUCCESS.name())")
    @Mapping(target = "request", source = "req", qualifiedByName = "stringIt")
    @Mapping(target = "response", source = "resp", qualifiedByName = "stringIt")
    @Mapping(target = "transactionType", source = "aggDetails.transactionType")
    @Mapping(target = "globalId", source = "aggDetails.globalId")
    @Mapping(target = "localCurrency", source = "postingDetails.localCurrencyCode")
    ReceivableAccountingGLEntity mapSuccessResponse(GlPostingResponse resp, GeneralLedgerEnvelope req, PostingDetails postingDetails, AggregatedGeneralLedgerTransactionDetails aggDetails);

    @Mapping(target = "sapDocNumber", ignore = true)
    @Mapping(target = "globalId", source = "aggDetails.globalId")
    @Mapping(target = "glPostingStatus", source = "resp", qualifiedByName = "mapErrorPostingStatus")
    @Mapping(target = "request", source = "req", qualifiedByName = "stringIt")
    @Mapping(target = "response", source = "resp", qualifiedByName = "stringIt")
    @Mapping(target = "transactionType", source = "aggDetails.transactionType")
    ReceivableAccountingGLEntity mapErrorResponse(GlPostingResponse resp, GeneralLedgerEnvelope req, AggregatedGeneralLedgerTransactionDetails aggDetails);

    @Named("mapErrorPostingStatus")
    default String mapErrorPostingStatus(GlPostingResponse response) {
        if (response.getErrorMessages() != null) {
            return getErrorStatus.apply(response);
        }
        return GLPOSTING_ERROR.name();
    }

    @Mapping(target = "sapDocNumber", ignore = true)
    @Mapping(target = "globalId", source = "aggDetails.globalId")
    @Mapping(target = "glPostingStatus",expression = "java(GlResponseEnum.GLPOSTING_FAILEDCONNECTION.name())")
    @Mapping(target = "request", source = "req", qualifiedByName = "stringIt")
    @Mapping(target = "response", source = "sapException.body", qualifiedByName = "stringIt")
    @Mapping(target = "transactionType", source = "aggDetails.transactionType")
    ReceivableAccountingGLEntity mapSap5xxException(SapResponseException sapException, GeneralLedgerEnvelope req, AggregatedGeneralLedgerTransactionDetails aggDetails);

    @Mapping(target = "sapDocNumber", ignore = true)
    @Mapping(target = "companyCode", ignore = true)
    @Mapping(target = "glPostYear", ignore = true)
    @Mapping(target = "glPostingStatus", ignore = true)
    @Mapping(target = "request", source = "request", qualifiedByName = "stringIt")
    @Mapping(target = "response", source = "response", qualifiedByName = "stringIt")
    @Mapping(target = "createdTimestamp", ignore = true)
    ReceivableAccountingGLEntity map(SapRequest request, SapResponse response, ErrorItemData errorItem);

    @AfterMapping
    default void setTimestamp(@MappingTarget ReceivableAccountingGLEntity accountingGLEntity){
        accountingGLEntity.setCreatedTimestamp(Timestamp.from(Instant.now()));
    }

    @Named("stringIt")
    default String stringIt(Object response){
        return new Gson().toJson(response);
    }

}
