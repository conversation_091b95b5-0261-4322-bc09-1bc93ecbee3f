package com.aa.ccrecon.gl.mapper;

import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.gl.entity.ReceivableAccountingGLSummaryEntity;
import com.aa.ccrecon.gl.model.sap.response.PostingDetails;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Arrays;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {Arrays.class})
public interface RecAccGlSummaryMapper {

    @Mapping(target = "sapDocNumber", source = "postingDetails.sapOperationalDocumentId")
    @Mapping(target = "companyCode", source = "postingDetails.companyCode")
    @Mapping(target = "glPostYear", source = "postingDetails.postingYear")
    @Mapping(target = "localCurrency", source = "postingDetails.localCurrencyCode")
    @Mapping(target = "globalId", source = "aggDetails.globalId")
    @Mapping(target = "responseSubTotal", source = "postingDetails.postingAmountLocalCurrency")
    @Mapping(target = "usdConvertedSubTotal", source = "postingDetails.postingAmountUsd")
    @Mapping(target = "transactionType", source = "aggDetails.transactionType")
    ReceivableAccountingGLSummaryEntity map(PostingDetails postingDetails, AggregatedGeneralLedgerTransactionDetails aggDetails);
}
