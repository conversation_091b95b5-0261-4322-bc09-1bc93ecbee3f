apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: 'itfacs-pmt-ccrecon-rcvbl-acctng-gl'
  description: 'itfacs-pmt-ccrecon-rcvbl-acctng-gl'
  annotations:
    github.com/project-slug: 'AAInternal/itfacs-pmt-ccrecon-rcvbl-acctng-gl'
    argocd/app-selector: 'backstage-name=itfacs-pmt-ccrecon-rcvbl-acctng-gl'
    backstage.io/kubernetes-label-selector: 'backstage.io/kubernetes-id=itfacs-pmt-ccrecon-rcvbl-acctng-gl'
  tags:
    - java
    - spring-api
spec:
  type: service
  lifecycle: experimental
  owner: "payments-processing-system"
  providesApis:
    - 'itfacs-pmt-ccrecon-rcvbl-acctng-gl-api'
