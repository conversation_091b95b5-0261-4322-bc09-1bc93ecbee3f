name: Validate catalog-info.yaml
on:
  push:
    branches: [ main ]
  pull_request:
    types: [opened, synchronize, ready_for_review]
    branches: [ main ]
jobs:
  validate-catalog-info-yaml:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Validate catalog-info yaml file
        uses: AAInternal/backstage-catalog-info-yaml-validator@v3.0.3
        with:
          path: 'catalog-info.yaml'