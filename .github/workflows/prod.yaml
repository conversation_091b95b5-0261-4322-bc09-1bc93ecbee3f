name: Prod - B<PERSON>, Promote, Deploy

on:
  workflow_dispatch:
    inputs:
      changeArtifactLocation:
        description: 'Link for the User Story in ADO'
        type: string
        required: true
      
      cherwellDescription:
        description: 'Description for the Cherwell Request'
        type: string
        required: true
      cherwellTeam:
        description: 'Team Name'
        type: choice
        required: true
        options:
          - 'Payments Squad J_Jedi'
          - 'Team H_Payviators'
      cherwellStartDateTime:
        description: 'Start Date Time for the Cherwell Request in UTC (YYYY-MM-DDTHH:MM:SSZ)'
        type: string
        required: true
      cherwellEndDateTime:
        description: 'End Date Time for the Cherwell Request in UTC (YYYY-MM-DDTHH:MM:SSZ)'
        type: string
        required: true

jobs:
  build-and-promote:
    name: Build and Promote Application to Production
    uses: AAInternal/itfacs-payments-workflows/.github/workflows/ru_docker_promote_prod.yml@v5
    permissions:
      id-token: write
      pull-requests: write
      contents: write
      issues: write
      security-events: write
      actions: read
    secrets: inherit
    with:
      applicationName: ${{ github.event.repository.name }}
      changeArtifactLocation: ${{ github.event.inputs.changeArtifactLocation }}
      cherwellDescription: ${{ github.event.inputs.cherwellDescription }}
      cherwellTeam: ${{ github.event.inputs.cherwellTeam }}
      cherwellStartDateTime: ${{ github.event.inputs.cherwellStartDateTime }}
      cherwellEndDateTime: ${{ github.event.inputs.cherwellEndDateTime }}
      cronjob: false
      javaVersion: 24
      squad360Name: 'Team H,Team J,Team 5'