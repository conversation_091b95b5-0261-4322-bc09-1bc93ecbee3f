name: Non-Prod - Build, Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'
        type: choice
        default: dev
        required: true
        options:
          - dev
          - stage

jobs:
  build-and-publish:
    name: Build and Publish Application
    uses: AAInternal/itfacs-payments-workflows/.github/workflows/ru_maven_docker.yml@v5
    permissions:
      id-token: write
      pull-requests: write
      contents: write
      issues: write
      security-events: write
      actions: read
    secrets: inherit
    with:
      applicationName: ${{ github.event.repository.name }}
      environment: ${{ github.event.inputs.environment }}
      javaVersion: 24
      runCodeCoverage: ${{ github.event.inputs.environment == 'dev' }}
      cronjob: false
  success:
    name: notify teams of completed  job
    permissions:
      contents: read
    runs-on: ubuntu-latest
    if: always()
    needs: build-and-publish
    steps:
      - uses: actions/checkout@master
      - name: Microsoft Teams Notification
        uses: JBallard419AA/notify-microsoft-teams@master
        if: always()
        with:
          webhook_url: ${{ secrets.MS_TEAMS_WEBHOOK_URI }}
          needs: ${{ to<PERSON><PERSON>(needs) }}
          job: ${{ to<PERSON><PERSON>(job) }}
          steps: ${{ to<PERSON>son(steps) }}
          env: ${{ github.event.inputs.environment }}
          workflow_status: ${{needs.build-and-publish.result}}