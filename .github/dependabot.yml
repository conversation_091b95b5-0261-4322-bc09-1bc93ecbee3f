# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
registries:
  artifactory:
    type: maven-repository
    url: https://packages.aa.com/artifactory/maven-public/
    username: ${{secrets.ARTIFACTORY_CRED_USR}}
    password: ${{secrets.ARTIFACTORY_CRED_PAT}}
  artifactory-docker:
    type: docker-registry
    url: https://packages.aa.com/
    username: ${{secrets.ARTIFACTORY_CRED_USR}}
    password: ${{secrets.ARTIFACTORY_CRED_PAT}}
updates:
  - package-ecosystem: maven # See documentation for possible values
    directory: "/" # Location of package manifests
    registries:
      - artifactory
    schedule:
      interval: "daily"
    commit-message:
      prefix: 'fix(deps)'
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]

  - package-ecosystem: "github-actions"
    # Workflow files stored in the
    # default location of `.github/workflows`
    directory: "/"
    schedule:
      interval: "daily"
    commit-message:
      prefix: 'fix(deps)'
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]
      
  - package-ecosystem: "docker" # See documentation for possible values
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"
    registries:
      - artifactory-docker
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]